{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/%D9%81%D9%8A%D8%AC%D9%88%D9%84/New%20folder%20%282%29/sales-app/src/components/SalesForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface Product {\n  id: number\n  name: string\n  price: number\n  stock: number\n}\n\ninterface Customer {\n  id: number\n  name: string\n  email?: string\n}\n\ninterface SaleItem {\n  productId: number\n  quantity: number\n  unitPrice: number\n}\n\nexport default function SalesForm() {\n  const [customers, setCustomers] = useState<Customer[]>([])\n  const [products, setProducts] = useState<Product[]>([])\n  const [selectedCustomer, setSelectedCustomer] = useState('')\n  const [saleItems, setSaleItems] = useState<SaleItem[]>([])\n  const [discount, setDiscount] = useState(0)\n  const [tax, setTax] = useState(0)\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    fetchCustomers()\n    fetchProducts()\n  }, [])\n\n  const fetchCustomers = async () => {\n    try {\n      const response = await fetch('/api/customers')\n      const data = await response.json()\n      setCustomers(data)\n    } catch (error) {\n      console.error('Error fetching customers:', error)\n    }\n  }\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products')\n      const data = await response.json()\n      setProducts(data)\n    } catch (error) {\n      console.error('Error fetching products:', error)\n    }\n  }\n\n  const addSaleItem = () => {\n    setSaleItems([...saleItems, { productId: 0, quantity: 1, unitPrice: 0 }])\n  }\n\n  const updateSaleItem = (index: number, field: keyof SaleItem, value: number) => {\n    const updatedItems = [...saleItems]\n    updatedItems[index] = { ...updatedItems[index], [field]: value }\n    \n    if (field === 'productId') {\n      const product = products.find(p => p.id === value)\n      if (product) {\n        updatedItems[index].unitPrice = product.price\n      }\n    }\n    \n    setSaleItems(updatedItems)\n  }\n\n  const removeSaleItem = (index: number) => {\n    setSaleItems(saleItems.filter((_, i) => i !== index))\n  }\n\n  const calculateTotal = () => {\n    const subtotal = saleItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)\n    return subtotal - discount + tax\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!selectedCustomer || saleItems.length === 0) {\n      alert('يرجى اختيار عميل وإضافة منتجات')\n      return\n    }\n\n    setLoading(true)\n    try {\n      const response = await fetch('/api/sales', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          customerId: selectedCustomer,\n          items: saleItems,\n          discount,\n          tax,\n        }),\n      })\n\n      if (response.ok) {\n        alert('تم إنشاء المبيعة بنجاح!')\n        setSelectedCustomer('')\n        setSaleItems([])\n        setDiscount(0)\n        setTax(0)\n        window.location.reload()\n      } else {\n        alert('فشل في إنشاء المبيعة')\n      }\n    } catch (error) {\n      console.error('Error creating sale:', error)\n      alert('حدث خطأ أثناء إنشاء المبيعة')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <h2 className=\"text-xl font-semibold mb-4\">إنشاء مبيعة جديدة</h2>\n      \n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        {/* اختيار العميل */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            العميل\n          </label>\n          <select\n            value={selectedCustomer}\n            onChange={(e) => setSelectedCustomer(e.target.value)}\n            className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            required\n          >\n            <option value=\"\">اختر عميل</option>\n            {customers.map((customer) => (\n              <option key={customer.id} value={customer.id}>\n                {customer.name}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* عناصر المبيعة */}\n        <div>\n          <div className=\"flex justify-between items-center mb-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">\n              المنتجات\n            </label>\n            <button\n              type=\"button\"\n              onClick={addSaleItem}\n              className=\"bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600\"\n            >\n              إضافة منتج\n            </button>\n          </div>\n          \n          {saleItems.map((item, index) => (\n            <div key={index} className=\"flex gap-2 mb-2 items-end\">\n              <div className=\"flex-1\">\n                <select\n                  value={item.productId}\n                  onChange={(e) => updateSaleItem(index, 'productId', parseInt(e.target.value))}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  required\n                >\n                  <option value={0}>اختر منتج</option>\n                  {products.map((product) => (\n                    <option key={product.id} value={product.id}>\n                      {product.name} - {product.price} ريال (متوفر: {product.stock})\n                    </option>\n                  ))}\n                </select>\n              </div>\n              <div className=\"w-20\">\n                <input\n                  type=\"number\"\n                  placeholder=\"الكمية\"\n                  value={item.quantity}\n                  onChange={(e) => updateSaleItem(index, 'quantity', parseInt(e.target.value) || 0)}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  min=\"1\"\n                  required\n                />\n              </div>\n              <div className=\"w-24\">\n                <input\n                  type=\"number\"\n                  placeholder=\"السعر\"\n                  value={item.unitPrice}\n                  onChange={(e) => updateSaleItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  required\n                />\n              </div>\n              <button\n                type=\"button\"\n                onClick={() => removeSaleItem(index)}\n                className=\"bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600\"\n              >\n                حذف\n              </button>\n            </div>\n          ))}\n        </div>\n\n        {/* الخصم والضريبة */}\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              الخصم (ريال)\n            </label>\n            <input\n              type=\"number\"\n              value={discount}\n              onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}\n              className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n              step=\"0.01\"\n              min=\"0\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              الضريبة (ريال)\n            </label>\n            <input\n              type=\"number\"\n              value={tax}\n              onChange={(e) => setTax(parseFloat(e.target.value) || 0)}\n              className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n              step=\"0.01\"\n              min=\"0\"\n            />\n          </div>\n        </div>\n\n        {/* المجموع */}\n        <div className=\"bg-gray-50 p-4 rounded-md\">\n          <div className=\"text-lg font-semibold\">\n            المجموع الإجمالي: {calculateTotal().toFixed(2)} ريال\n          </div>\n        </div>\n\n        {/* زر الإرسال */}\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 disabled:opacity-50\"\n        >\n          {loading ? 'جاري الحفظ...' : 'إنشاء المبيعة'}\n        </button>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAuBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;eAAI;YAAW;gBAAE,WAAW;gBAAG,UAAU;gBAAG,WAAW;YAAE;SAAE;IAC1E;IAEA,MAAM,iBAAiB,CAAC,OAAe,OAAuB;QAC5D,MAAM,eAAe;eAAI;SAAU;QACnC,YAAY,CAAC,MAAM,GAAG;YAAE,GAAG,YAAY,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QAE/D,IAAI,UAAU,aAAa;YACzB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,IAAI,SAAS;gBACX,YAAY,CAAC,MAAM,CAAC,SAAS,GAAG,QAAQ,KAAK;YAC/C;QACF;QAEA,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAChD;IAEA,MAAM,iBAAiB;QACrB,MAAM,WAAW,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,QAAQ,GAAG,KAAK,SAAS,EAAG;QACzF,OAAO,WAAW,WAAW;IAC/B;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,oBAAoB,UAAU,MAAM,KAAK,GAAG;YAC/C,MAAM;YACN;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY;oBACZ,OAAO;oBACP;oBACA;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,oBAAoB;gBACpB,aAAa,EAAE;gBACf,YAAY;gBACZ,OAAO;gBACP,OAAO,QAAQ,CAAC,MAAM;YACxB,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAE3C,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;gCACV,QAAQ;;kDAER,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;4CAAyB,OAAO,SAAS,EAAE;sDACzC,SAAS,IAAI;2CADH,SAAS,EAAE;;;;;;;;;;;;;;;;;kCAQ9B,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA0C;;;;;;kDAG3D,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAKF,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,OAAO,KAAK,SAAS;gDACrB,UAAU,CAAC,IAAM,eAAe,OAAO,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC3E,WAAU;gDACV,QAAQ;;kEAER,8OAAC;wDAAO,OAAO;kEAAG;;;;;;oDACjB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4DAAwB,OAAO,QAAQ,EAAE;;gEACvC,QAAQ,IAAI;gEAAC;gEAAI,QAAQ,KAAK;gEAAC;gEAAe,QAAQ,KAAK;gEAAC;;2DADlD,QAAQ,EAAE;;;;;;;;;;;;;;;;sDAM7B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,KAAK,QAAQ;gDACpB,UAAU,CAAC,IAAM,eAAe,OAAO,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC/E,WAAU;gDACV,KAAI;gDACJ,QAAQ;;;;;;;;;;;sDAGZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,KAAK,SAAS;gDACrB,UAAU,CAAC,IAAM,eAAe,OAAO,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDAClF,WAAU;gDACV,MAAK;gDACL,KAAI;gDACJ,QAAQ;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;mCA3CO;;;;;;;;;;;kCAmDd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wCAC3D,WAAU;wCACV,MAAK;wCACL,KAAI;;;;;;;;;;;;0CAGR,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wCACtD,WAAU;wCACV,MAAK;wCACL,KAAI;;;;;;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCAAwB;gCAClB,iBAAiB,OAAO,CAAC;gCAAG;;;;;;;;;;;;kCAKnD,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/%D9%81%D9%8A%D8%AC%D9%88%D9%84/New%20folder%20%282%29/sales-app/src/components/SalesList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface Sale {\n  id: number\n  customer: {\n    name: string\n    email?: string\n  }\n  total: number\n  discount: number\n  tax: number\n  finalTotal: number\n  status: string\n  createdAt: string\n  saleItems: {\n    id: number\n    quantity: number\n    unitPrice: number\n    total: number\n    product: {\n      name: string\n    }\n  }[]\n}\n\nexport default function SalesList() {\n  const [sales, setSales] = useState<Sale[]>([])\n  const [loading, setLoading] = useState(true)\n  const [expandedSale, setExpandedSale] = useState<number | null>(null)\n\n  useEffect(() => {\n    fetchSales()\n  }, [])\n\n  const fetchSales = async () => {\n    try {\n      const response = await fetch('/api/sales')\n      const data = await response.json()\n      setSales(data)\n    } catch (error) {\n      console.error('Error fetching sales:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('ar-SA', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800'\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'cancelled':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'مكتملة'\n      case 'pending':\n        return 'معلقة'\n      case 'cancelled':\n        return 'ملغية'\n      default:\n        return status\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"text-center\">جاري تحميل المبيعات...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white shadow rounded-lg p-6\">\n      <h2 className=\"text-xl font-semibold mb-4\">قائمة المبيعات</h2>\n      \n      {sales.length === 0 ? (\n        <div className=\"text-center text-gray-500 py-8\">\n          لا توجد مبيعات حتى الآن\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {sales.map((sale) => (\n            <div key={sale.id} className=\"border border-gray-200 rounded-lg p-4\">\n              <div className=\"flex justify-between items-start\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-4 mb-2\">\n                    <h3 className=\"font-semibold text-lg\">\n                      مبيعة #{sale.id}\n                    </h3>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(sale.status)}`}>\n                      {getStatusText(sale.status)}\n                    </span>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-500\">العميل:</span>\n                      <div className=\"font-medium\">{sale.customer.name}</div>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-500\">التاريخ:</span>\n                      <div className=\"font-medium\">{formatDate(sale.createdAt)}</div>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-500\">المجموع الفرعي:</span>\n                      <div className=\"font-medium\">{sale.total.toFixed(2)} ريال</div>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-500\">المجموع النهائي:</span>\n                      <div className=\"font-medium text-green-600\">{sale.finalTotal.toFixed(2)} ريال</div>\n                    </div>\n                  </div>\n\n                  {sale.discount > 0 && (\n                    <div className=\"mt-2 text-sm\">\n                      <span className=\"text-gray-500\">الخصم:</span>\n                      <span className=\"font-medium text-red-600 mr-2\">{sale.discount.toFixed(2)} ريال</span>\n                    </div>\n                  )}\n\n                  {sale.tax > 0 && (\n                    <div className=\"mt-1 text-sm\">\n                      <span className=\"text-gray-500\">الضريبة:</span>\n                      <span className=\"font-medium mr-2\">{sale.tax.toFixed(2)} ريال</span>\n                    </div>\n                  )}\n                </div>\n\n                <button\n                  onClick={() => setExpandedSale(expandedSale === sale.id ? null : sale.id)}\n                  className=\"text-blue-500 hover:text-blue-700 text-sm font-medium\"\n                >\n                  {expandedSale === sale.id ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}\n                </button>\n              </div>\n\n              {expandedSale === sale.id && (\n                <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                  <h4 className=\"font-medium mb-3\">تفاصيل المنتجات:</h4>\n                  <div className=\"space-y-2\">\n                    {sale.saleItems.map((item) => (\n                      <div key={item.id} className=\"flex justify-between items-center bg-gray-50 p-3 rounded\">\n                        <div>\n                          <div className=\"font-medium\">{item.product.name}</div>\n                          <div className=\"text-sm text-gray-500\">\n                            {item.quantity} × {item.unitPrice.toFixed(2)} ريال\n                          </div>\n                        </div>\n                        <div className=\"font-medium\">\n                          {item.total.toFixed(2)} ريال\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA2Be,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAc;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA6B;;;;;;YAE1C,MAAM,MAAM,KAAK,kBAChB,8OAAC;gBAAI,WAAU;0BAAiC;;;;;qCAIhD,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;4DAAwB;4DAC5B,KAAK,EAAE;;;;;;;kEAEjB,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;kEACzF,cAAc,KAAK,MAAM;;;;;;;;;;;;0DAI9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAI,WAAU;0EAAe,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;kEAElD,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAI,WAAU;0EAAe,WAAW,KAAK,SAAS;;;;;;;;;;;;kEAEzD,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAI,WAAU;;oEAAe,KAAK,KAAK,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEtD,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAI,WAAU;;oEAA8B,KAAK,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;4CAI3E,KAAK,QAAQ,GAAG,mBACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;;4DAAiC,KAAK,QAAQ,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;4CAI7E,KAAK,GAAG,GAAG,mBACV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;;4DAAoB,KAAK,GAAG,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAK9D,8OAAC;wCACC,SAAS,IAAM,gBAAgB,iBAAiB,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE;wCACxE,WAAU;kDAET,iBAAiB,KAAK,EAAE,GAAG,mBAAmB;;;;;;;;;;;;4BAIlD,iBAAiB,KAAK,EAAE,kBACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,8OAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,qBACnB,8OAAC;gDAAkB,WAAU;;kEAC3B,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAe,KAAK,OAAO,CAAC,IAAI;;;;;;0EAC/C,8OAAC;gEAAI,WAAU;;oEACZ,KAAK,QAAQ;oEAAC;oEAAI,KAAK,SAAS,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAGjD,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,KAAK,CAAC,OAAO,CAAC;4DAAG;;;;;;;;+CARjB,KAAK,EAAE;;;;;;;;;;;;;;;;;uBA3DjB,KAAK,EAAE;;;;;;;;;;;;;;;;AAgF7B", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/%D9%81%D9%8A%D8%AC%D9%88%D9%84/New%20folder%20%282%29/sales-app/src/components/ProductsManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface Product {\n  id: number\n  name: string\n  description?: string\n  price: number\n  stock: number\n  category?: string\n  createdAt: string\n}\n\nexport default function ProductsManager() {\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showForm, setShowForm] = useState(false)\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category: ''\n  })\n\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products')\n      const data = await response.json()\n      setProducts(data)\n    } catch (error) {\n      console.error('Error fetching products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    try {\n      const response = await fetch('/api/products', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      if (response.ok) {\n        alert('تم إضافة المنتج بنجاح!')\n        setFormData({\n          name: '',\n          description: '',\n          price: '',\n          stock: '',\n          category: ''\n        })\n        setShowForm(false)\n        fetchProducts()\n      } else {\n        alert('فشل في إضافة المنتج')\n      }\n    } catch (error) {\n      console.error('Error adding product:', error)\n      alert('حدث خطأ أثناء إضافة المنتج')\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"text-center\">جاري تحميل المنتجات...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex justify-between items-center\">\n          <h2 className=\"text-xl font-semibold\">إدارة المنتجات</h2>\n          <button\n            onClick={() => setShowForm(!showForm)}\n            className=\"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600\"\n          >\n            {showForm ? 'إلغاء' : 'إضافة منتج جديد'}\n          </button>\n        </div>\n\n        {/* Add Product Form */}\n        {showForm && (\n          <form onSubmit={handleSubmit} className=\"mt-6 space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  اسم المنتج *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  الفئة\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"category\"\n                  value={formData.category}\n                  onChange={handleInputChange}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  السعر (ريال) *\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"price\"\n                  value={formData.price}\n                  onChange={handleInputChange}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  الكمية المتوفرة\n                </label>\n                <input\n                  type=\"number\"\n                  name=\"stock\"\n                  value={formData.stock}\n                  onChange={handleInputChange}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  min=\"0\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الوصف\n              </label>\n              <textarea\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                rows={3}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <button\n              type=\"submit\"\n              className=\"bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600\"\n            >\n              إضافة المنتج\n            </button>\n          </form>\n        )}\n      </div>\n\n      {/* Products List */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">قائمة المنتجات</h3>\n        \n        {products.length === 0 ? (\n          <div className=\"text-center text-gray-500 py-8\">\n            لا توجد منتجات حتى الآن\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    المنتج\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    الفئة\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    السعر\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    المخزون\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    تاريخ الإضافة\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {products.map((product) => (\n                  <tr key={product.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {product.name}\n                        </div>\n                        {product.description && (\n                          <div className=\"text-sm text-gray-500\">\n                            {product.description}\n                          </div>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {product.category || '-'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {product.price.toFixed(2)} ريال\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        product.stock > 10 \n                          ? 'bg-green-100 text-green-800'\n                          : product.stock > 0\n                          ? 'bg-yellow-100 text-yellow-800'\n                          : 'bg-red-100 text-red-800'\n                      }`}>\n                        {product.stock}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {new Date(product.createdAt).toLocaleDateString('ar-SA')}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,OAAO;oBACP,OAAO;oBACP,UAAU;gBACZ;gBACA,YAAY;gBACZ;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAc;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCACC,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAET,WAAW,UAAU;;;;;;;;;;;;oBAKzB,0BACC,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,WAAU;gDACV,MAAK;gDACL,KAAI;gDACJ,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,WAAU;gDACV,KAAI;;;;;;;;;;;;;;;;;;0CAIV,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,MAAM;wCACN,WAAU;;;;;;;;;;;;0CAGd,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;oBAE1C,SAAS,MAAM,KAAK,kBACnB,8OAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkF;;;;;;0DAGhG,8OAAC;gDAAG,WAAU;0DAAkF;;;;;;0DAGhG,8OAAC;gDAAG,WAAU;0DAAkF;;;;;;0DAGhG,8OAAC;gDAAG,WAAU;0DAAkF;;;;;;0DAGhG,8OAAC;gDAAG,WAAU;0DAAkF;;;;;;;;;;;;;;;;;8CAKpG,8OAAC;oCAAM,WAAU;8CACd,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI;;;;;;4DAEd,QAAQ,WAAW,kBAClB,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;;;;;;8DAK5B,8OAAC;oDAAG,WAAU;8DACX,QAAQ,QAAQ,IAAI;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;;wDACX,QAAQ,KAAK,CAAC,OAAO,CAAC;wDAAG;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,yDAAyD,EACzE,QAAQ,KAAK,GAAG,KACZ,gCACA,QAAQ,KAAK,GAAG,IAChB,kCACA,2BACJ;kEACC,QAAQ,KAAK;;;;;;;;;;;8DAGlB,8OAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;2CA/B3C,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CrC", "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/%D9%81%D9%8A%D8%AC%D9%88%D9%84/New%20folder%20%282%29/sales-app/src/components/CustomersManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface Customer {\n  id: number\n  name: string\n  email?: string\n  phone?: string\n  address?: string\n  createdAt: string\n}\n\nexport default function CustomersManager() {\n  const [customers, setCustomers] = useState<Customer[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showForm, setShowForm] = useState(false)\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: ''\n  })\n\n  useEffect(() => {\n    fetchCustomers()\n  }, [])\n\n  const fetchCustomers = async () => {\n    try {\n      const response = await fetch('/api/customers')\n      const data = await response.json()\n      setCustomers(data)\n    } catch (error) {\n      console.error('Error fetching customers:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    try {\n      const response = await fetch('/api/customers', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      if (response.ok) {\n        alert('تم إضافة العميل بنجاح!')\n        setFormData({\n          name: '',\n          email: '',\n          phone: '',\n          address: ''\n        })\n        setShowForm(false)\n        fetchCustomers()\n      } else {\n        alert('فشل في إضافة العميل')\n      }\n    } catch (error) {\n      console.error('Error adding customer:', error)\n      alert('حدث خطأ أثناء إضافة العميل')\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"text-center\">جاري تحميل العملاء...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex justify-between items-center\">\n          <h2 className=\"text-xl font-semibold\">إدارة العملاء</h2>\n          <button\n            onClick={() => setShowForm(!showForm)}\n            className=\"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600\"\n          >\n            {showForm ? 'إلغاء' : 'إضافة عميل جديد'}\n          </button>\n        </div>\n\n        {/* Add Customer Form */}\n        {showForm && (\n          <form onSubmit={handleSubmit} className=\"mt-6 space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  اسم العميل *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  البريد الإلكتروني\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  رقم الهاتف\n                </label>\n                <input\n                  type=\"tel\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  العنوان\n                </label>\n                <textarea\n                  name=\"address\"\n                  value={formData.address}\n                  onChange={handleInputChange}\n                  rows={3}\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n            <button\n              type=\"submit\"\n              className=\"bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600\"\n            >\n              إضافة العميل\n            </button>\n          </form>\n        )}\n      </div>\n\n      {/* Customers List */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">قائمة العملاء</h3>\n        \n        {customers.length === 0 ? (\n          <div className=\"text-center text-gray-500 py-8\">\n            لا يوجد عملاء حتى الآن\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {customers.map((customer) => (\n              <div key={customer.id} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-semibold text-lg text-gray-900 mb-2\">\n                      {customer.name}\n                    </h4>\n                    \n                    {customer.email && (\n                      <div className=\"flex items-center text-sm text-gray-600 mb-1\">\n                        <span className=\"ml-2\">📧</span>\n                        {customer.email}\n                      </div>\n                    )}\n                    \n                    {customer.phone && (\n                      <div className=\"flex items-center text-sm text-gray-600 mb-1\">\n                        <span className=\"ml-2\">📱</span>\n                        {customer.phone}\n                      </div>\n                    )}\n                    \n                    {customer.address && (\n                      <div className=\"flex items-start text-sm text-gray-600 mb-2\">\n                        <span className=\"ml-2 mt-0.5\">📍</span>\n                        <span>{customer.address}</span>\n                      </div>\n                    )}\n                    \n                    <div className=\"text-xs text-gray-500 mt-3\">\n                      تاريخ التسجيل: {new Date(customer.createdAt).toLocaleDateString('ar-SA')}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAae,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP,OAAO;oBACP,SAAS;gBACX;gBACA,YAAY;gBACZ;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAc;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCACC,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAET,WAAW,UAAU;;;;;;;;;;;;oBAKzB,0BACC,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,MAAM;gDACN,WAAU;;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;oBAE1C,UAAU,MAAM,KAAK,kBACpB,8OAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;gCAAsB,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;4CAGf,SAAS,KAAK,kBACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAO;;;;;;oDACtB,SAAS,KAAK;;;;;;;4CAIlB,SAAS,KAAK,kBACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAO;;;;;;oDACtB,SAAS,KAAK;;;;;;;4CAIlB,SAAS,OAAO,kBACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,8OAAC;kEAAM,SAAS,OAAO;;;;;;;;;;;;0DAI3B,8OAAC;gDAAI,WAAU;;oDAA6B;oDAC1B,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;+BA7B9D,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AAwCnC", "debugId": null}}, {"offset": {"line": 1742, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/%D9%81%D9%8A%D8%AC%D9%88%D9%84/New%20folder%20%282%29/sales-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport SalesForm from '@/components/SalesForm'\nimport SalesList from '@/components/SalesList'\nimport ProductsManager from '@/components/ProductsManager'\nimport CustomersManager from '@/components/CustomersManager'\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState('sales')\n\n  const tabs = [\n    { id: 'sales', name: 'المبيعات', icon: '💰' },\n    { id: 'products', name: 'المنتجات', icon: '📦' },\n    { id: 'customers', name: 'العملاء', icon: '👥' },\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">نظام إدارة المبيعات</h1>\n            <div className=\"text-sm text-gray-500\">\n              {new Date().toLocaleDateString('ar-SA')}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex space-x-8 space-x-reverse\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <span className=\"ml-2\">{tab.icon}</span>\n                {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {activeTab === 'sales' && (\n            <div className=\"space-y-6\">\n              <SalesForm />\n              <SalesList />\n            </div>\n          )}\n          {activeTab === 'products' && <ProductsManager />}\n          {activeTab === 'customers' && <CustomersManager />}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,MAAM;YAAY,MAAM;QAAK;QAC5C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAa,MAAM;YAAW,MAAM;QAAK;KAChD;IAED,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;0CACZ,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;;kDAEF,8OAAC;wCAAK,WAAU;kDAAQ,IAAI,IAAI;;;;;;oCAC/B,IAAI,IAAI;;+BATJ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;0BAiBrB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,cAAc,yBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,+HAAA,CAAA,UAAS;;;;;8CACV,8OAAC,+HAAA,CAAA,UAAS;;;;;;;;;;;wBAGb,cAAc,4BAAc,8OAAC,qIAAA,CAAA,UAAe;;;;;wBAC5C,cAAc,6BAAe,8OAAC,sIAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;;;;;;AAKzD", "debugId": null}}]}