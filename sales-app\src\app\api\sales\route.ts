import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET - جلب جميع المبيعات
export async function GET() {
  try {
    const sales = await prisma.sale.findMany({
      include: {
        customer: true,
        saleItems: {
          include: {
            product: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
    return NextResponse.json(sales)
  } catch (error) {
    return NextResponse.json(
      { error: 'فشل في جلب المبيعات' },
      { status: 500 }
    )
  }
}

// POST - إضافة مبيعة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { customerId, items, discount = 0, tax = 0 } = body

    if (!customerId || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'العميل وعناصر المبيعة مطلوبان' },
        { status: 400 }
      )
    }

    // حساب المجموع
    let total = 0
    for (const item of items) {
      total += item.quantity * item.unitPrice
    }

    const finalTotal = total - discount + tax

    // إنشاء المبيعة مع العناصر
    const sale = await prisma.sale.create({
      data: {
        customerId: parseInt(customerId),
        total,
        discount,
        tax,
        finalTotal,
        saleItems: {
          create: items.map((item: any) => ({
            productId: parseInt(item.productId),
            quantity: parseInt(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            total: parseInt(item.quantity) * parseFloat(item.unitPrice)
          }))
        }
      },
      include: {
        customer: true,
        saleItems: {
          include: {
            product: true
          }
        }
      }
    })

    // تحديث المخزون
    for (const item of items) {
      await prisma.product.update({
        where: { id: parseInt(item.productId) },
        data: {
          stock: {
            decrement: parseInt(item.quantity)
          }
        }
      })
    }

    return NextResponse.json(sale, { status: 201 })
  } catch (error) {
    console.error('Error creating sale:', error)
    return NextResponse.json(
      { error: 'فشل في إضافة المبيعة' },
      { status: 500 }
    )
  }
}
