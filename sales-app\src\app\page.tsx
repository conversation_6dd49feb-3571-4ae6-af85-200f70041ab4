'use client'

import { useState, useEffect } from 'react'
import SalesForm from '@/components/SalesForm'
import SalesList from '@/components/SalesList'
import ProductsManager from '@/components/ProductsManager'
import CustomersManager from '@/components/CustomersManager'

export default function Home() {
  const [activeTab, setActiveTab] = useState('sales')

  const tabs = [
    { id: 'sales', name: 'المبيعات', icon: '💰' },
    { id: 'products', name: 'المنتجات', icon: '📦' },
    { id: 'customers', name: 'العملاء', icon: '👥' },
  ]

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">نظام إدارة المبيعات</h1>
            <div className="text-sm text-gray-500">
              {new Date().toLocaleDateString('ar-SA')}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 space-x-reverse">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="ml-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {activeTab === 'sales' && (
            <div className="space-y-6">
              <SalesForm />
              <SalesList />
            </div>
          )}
          {activeTab === 'products' && <ProductsManager />}
          {activeTab === 'customers' && <CustomersManager />}
        </div>
      </main>
    </div>
  )
}
