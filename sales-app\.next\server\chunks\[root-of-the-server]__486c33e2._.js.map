{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/%D9%81%D9%8A%D8%AC%D9%88%D9%84/New%20folder%20%282%29/sales-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/%D9%81%D9%8A%D8%AC%D9%88%D9%84/New%20folder%20%282%29/sales-app/src/app/api/sales/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\n// GET - جلب جميع المبيعات\nexport async function GET() {\n  try {\n    const sales = await prisma.sale.findMany({\n      include: {\n        customer: true,\n        saleItems: {\n          include: {\n            product: true\n          }\n        }\n      },\n      orderBy: { createdAt: 'desc' }\n    })\n    return NextResponse.json(sales)\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'فشل في جلب المبيعات' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة مبيعة جديدة\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { customerId, items, discount = 0, tax = 0 } = body\n\n    if (!customerId || !items || items.length === 0) {\n      return NextResponse.json(\n        { error: 'العميل وعناصر المبيعة مطلوبان' },\n        { status: 400 }\n      )\n    }\n\n    // حساب المجموع\n    let total = 0\n    for (const item of items) {\n      total += item.quantity * item.unitPrice\n    }\n\n    const finalTotal = total - discount + tax\n\n    // إنشاء المبيعة مع العناصر\n    const sale = await prisma.sale.create({\n      data: {\n        customerId: parseInt(customerId),\n        total,\n        discount,\n        tax,\n        finalTotal,\n        saleItems: {\n          create: items.map((item: any) => ({\n            productId: parseInt(item.productId),\n            quantity: parseInt(item.quantity),\n            unitPrice: parseFloat(item.unitPrice),\n            total: parseInt(item.quantity) * parseFloat(item.unitPrice)\n          }))\n        }\n      },\n      include: {\n        customer: true,\n        saleItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n\n    // تحديث المخزون\n    for (const item of items) {\n      await prisma.product.update({\n        where: { id: parseInt(item.productId) },\n        data: {\n          stock: {\n            decrement: parseInt(item.quantity)\n          }\n        }\n      })\n    }\n\n    return NextResponse.json(sale, { status: 201 })\n  } catch (error) {\n    console.error('Error creating sale:', error)\n    return NextResponse.json(\n      { error: 'فشل في إضافة المبيعة' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,SAAS;gBACP,UAAU;gBACV,WAAW;oBACT,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG;QAErD,IAAI,CAAC,cAAc,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,IAAI,QAAQ;QACZ,KAAK,MAAM,QAAQ,MAAO;YACxB,SAAS,KAAK,QAAQ,GAAG,KAAK,SAAS;QACzC;QAEA,MAAM,aAAa,QAAQ,WAAW;QAEtC,2BAA2B;QAC3B,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,YAAY,SAAS;gBACrB;gBACA;gBACA;gBACA;gBACA,WAAW;oBACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAc,CAAC;4BAChC,WAAW,SAAS,KAAK,SAAS;4BAClC,UAAU,SAAS,KAAK,QAAQ;4BAChC,WAAW,WAAW,KAAK,SAAS;4BACpC,OAAO,SAAS,KAAK,QAAQ,IAAI,WAAW,KAAK,SAAS;wBAC5D,CAAC;gBACH;YACF;YACA,SAAS;gBACP,UAAU;gBACV,WAAW;oBACT,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,gBAAgB;QAChB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC1B,OAAO;oBAAE,IAAI,SAAS,KAAK,SAAS;gBAAE;gBACtC,MAAM;oBACJ,OAAO;wBACL,WAAW,SAAS,KAAK,QAAQ;oBACnC;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}