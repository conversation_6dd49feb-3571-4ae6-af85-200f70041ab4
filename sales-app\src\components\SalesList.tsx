'use client'

import { useState, useEffect } from 'react'

interface Sale {
  id: number
  customer: {
    name: string
    email?: string
  }
  total: number
  discount: number
  tax: number
  finalTotal: number
  status: string
  createdAt: string
  saleItems: {
    id: number
    quantity: number
    unitPrice: number
    total: number
    product: {
      name: string
    }
  }[]
}

export default function SalesList() {
  const [sales, setSales] = useState<Sale[]>([])
  const [loading, setLoading] = useState(true)
  const [expandedSale, setExpandedSale] = useState<number | null>(null)

  useEffect(() => {
    fetchSales()
  }, [])

  const fetchSales = async () => {
    try {
      const response = await fetch('/api/sales')
      const data = await response.json()
      setSales(data)
    } catch (error) {
      console.error('Error fetching sales:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتملة'
      case 'pending':
        return 'معلقة'
      case 'cancelled':
        return 'ملغية'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="text-center">جاري تحميل المبيعات...</div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">قائمة المبيعات</h2>
      
      {sales.length === 0 ? (
        <div className="text-center text-gray-500 py-8">
          لا توجد مبيعات حتى الآن
        </div>
      ) : (
        <div className="space-y-4">
          {sales.map((sale) => (
            <div key={sale.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-2">
                    <h3 className="font-semibold text-lg">
                      مبيعة #{sale.id}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(sale.status)}`}>
                      {getStatusText(sale.status)}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">العميل:</span>
                      <div className="font-medium">{sale.customer.name}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">التاريخ:</span>
                      <div className="font-medium">{formatDate(sale.createdAt)}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">المجموع الفرعي:</span>
                      <div className="font-medium">{sale.total.toFixed(2)} ريال</div>
                    </div>
                    <div>
                      <span className="text-gray-500">المجموع النهائي:</span>
                      <div className="font-medium text-green-600">{sale.finalTotal.toFixed(2)} ريال</div>
                    </div>
                  </div>

                  {sale.discount > 0 && (
                    <div className="mt-2 text-sm">
                      <span className="text-gray-500">الخصم:</span>
                      <span className="font-medium text-red-600 mr-2">{sale.discount.toFixed(2)} ريال</span>
                    </div>
                  )}

                  {sale.tax > 0 && (
                    <div className="mt-1 text-sm">
                      <span className="text-gray-500">الضريبة:</span>
                      <span className="font-medium mr-2">{sale.tax.toFixed(2)} ريال</span>
                    </div>
                  )}
                </div>

                <button
                  onClick={() => setExpandedSale(expandedSale === sale.id ? null : sale.id)}
                  className="text-blue-500 hover:text-blue-700 text-sm font-medium"
                >
                  {expandedSale === sale.id ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
                </button>
              </div>

              {expandedSale === sale.id && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h4 className="font-medium mb-3">تفاصيل المنتجات:</h4>
                  <div className="space-y-2">
                    {sale.saleItems.map((item) => (
                      <div key={item.id} className="flex justify-between items-center bg-gray-50 p-3 rounded">
                        <div>
                          <div className="font-medium">{item.product.name}</div>
                          <div className="text-sm text-gray-500">
                            {item.quantity} × {item.unitPrice.toFixed(2)} ريال
                          </div>
                        </div>
                        <div className="font-medium">
                          {item.total.toFixed(2)} ريال
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
