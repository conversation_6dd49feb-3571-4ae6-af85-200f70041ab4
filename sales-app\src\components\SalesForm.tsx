'use client'

import { useState, useEffect } from 'react'

interface Product {
  id: number
  name: string
  price: number
  stock: number
}

interface Customer {
  id: number
  name: string
  email?: string
}

interface SaleItem {
  productId: number
  quantity: number
  unitPrice: number
}

export default function SalesForm() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState('')
  const [saleItems, setSaleItems] = useState<SaleItem[]>([])
  const [discount, setDiscount] = useState(0)
  const [tax, setTax] = useState(0)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchCustomers()
    fetchProducts()
  }, [])

  const fetchCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      const data = await response.json()
      setCustomers(data)
    } catch (error) {
      console.error('Error fetching customers:', error)
    }
  }

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products')
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('Error fetching products:', error)
    }
  }

  const addSaleItem = () => {
    setSaleItems([...saleItems, { productId: 0, quantity: 1, unitPrice: 0 }])
  }

  const updateSaleItem = (index: number, field: keyof SaleItem, value: number) => {
    const updatedItems = [...saleItems]
    updatedItems[index] = { ...updatedItems[index], [field]: value }
    
    if (field === 'productId') {
      const product = products.find(p => p.id === value)
      if (product) {
        updatedItems[index].unitPrice = product.price
      }
    }
    
    setSaleItems(updatedItems)
  }

  const removeSaleItem = (index: number) => {
    setSaleItems(saleItems.filter((_, i) => i !== index))
  }

  const calculateTotal = () => {
    const subtotal = saleItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
    return subtotal - discount + tax
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedCustomer || saleItems.length === 0) {
      alert('يرجى اختيار عميل وإضافة منتجات')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId: selectedCustomer,
          items: saleItems,
          discount,
          tax,
        }),
      })

      if (response.ok) {
        alert('تم إنشاء المبيعة بنجاح!')
        setSelectedCustomer('')
        setSaleItems([])
        setDiscount(0)
        setTax(0)
        window.location.reload()
      } else {
        alert('فشل في إنشاء المبيعة')
      }
    } catch (error) {
      console.error('Error creating sale:', error)
      alert('حدث خطأ أثناء إنشاء المبيعة')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">إنشاء مبيعة جديدة</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* اختيار العميل */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            العميل
          </label>
          <select
            value={selectedCustomer}
            onChange={(e) => setSelectedCustomer(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          >
            <option value="">اختر عميل</option>
            {customers.map((customer) => (
              <option key={customer.id} value={customer.id}>
                {customer.name}
              </option>
            ))}
          </select>
        </div>

        {/* عناصر المبيعة */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-700">
              المنتجات
            </label>
            <button
              type="button"
              onClick={addSaleItem}
              className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
            >
              إضافة منتج
            </button>
          </div>
          
          {saleItems.map((item, index) => (
            <div key={index} className="flex gap-2 mb-2 items-end">
              <div className="flex-1">
                <select
                  value={item.productId}
                  onChange={(e) => updateSaleItem(index, 'productId', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value={0}>اختر منتج</option>
                  {products.map((product) => (
                    <option key={product.id} value={product.id}>
                      {product.name} - {product.price} ريال (متوفر: {product.stock})
                    </option>
                  ))}
                </select>
              </div>
              <div className="w-20">
                <input
                  type="number"
                  placeholder="الكمية"
                  value={item.quantity}
                  onChange={(e) => updateSaleItem(index, 'quantity', parseInt(e.target.value) || 0)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  min="1"
                  required
                />
              </div>
              <div className="w-24">
                <input
                  type="number"
                  placeholder="السعر"
                  value={item.unitPrice}
                  onChange={(e) => updateSaleItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
              <button
                type="button"
                onClick={() => removeSaleItem(index)}
                className="bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600"
              >
                حذف
              </button>
            </div>
          ))}
        </div>

        {/* الخصم والضريبة */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الخصم (ريال)
            </label>
            <input
              type="number"
              value={discount}
              onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              step="0.01"
              min="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الضريبة (ريال)
            </label>
            <input
              type="number"
              value={tax}
              onChange={(e) => setTax(parseFloat(e.target.value) || 0)}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              step="0.01"
              min="0"
            />
          </div>
        </div>

        {/* المجموع */}
        <div className="bg-gray-50 p-4 rounded-md">
          <div className="text-lg font-semibold">
            المجموع الإجمالي: {calculateTotal().toFixed(2)} ريال
          </div>
        </div>

        {/* زر الإرسال */}
        <button
          type="submit"
          disabled={loading}
          className="w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'جاري الحفظ...' : 'إنشاء المبيعة'}
        </button>
      </form>
    </div>
  )
}
