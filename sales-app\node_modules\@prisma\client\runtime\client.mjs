import * as __banner_node_module from "node:module";
import * as __banner_node_path from "node:path";
import * as process from "node:process";
import * as __banner_node_url from "node:url";
const __filename = __banner_node_url.fileURLToPath(import.meta.url);
const __dirname = __banner_node_path.dirname(__filename);
const require = __banner_node_module.createRequire(import.meta.url);
var nc=Object.create;var Zn=Object.defineProperty;var ic=Object.getOwnPropertyDescriptor;var oc=Object.getOwnPropertyNames;var sc=Object.getPrototypeOf,ac=Object.prototype.hasOwnProperty;var Vt=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var Lo=(e,t)=>()=>(e&&(t=e(e=0)),t);var G=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),qt=(e,t)=>{for(var r in t)Zn(e,r,{get:t[r],enumerable:!0})},lc=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of oc(t))!ac.call(e,i)&&i!==r&&Zn(e,i,{get:()=>t[i],enumerable:!(n=ic(t,i))||n.enumerable});return e};var ye=(e,t,r)=>(r=e!=null?nc(sc(e)):{},lc(t||!e||!e.__esModule?Zn(r,"default",{value:e,enumerable:!0}):r,e));var Zo=G((nh,kc)=>{kc.exports={name:"@prisma/internals",version:"6.9.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/schema-engine-wasm":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var es=G((sh,Xo)=>{"use strict";Xo.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((r,n)=>Math.min(r,n.length),1/0):0}});var is=G((uh,ns)=>{"use strict";ns.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var as=G((ph,ss)=>{"use strict";ss.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var oi=G((dh,ls)=>{"use strict";var $c=as();ls.exports=e=>typeof e=="string"?e.replace($c(),""):e});var us=G((hh,Vc)=>{Vc.exports={name:"dotenv",version:"16.5.0",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},homepage:"https://github.com/motdotla/dotenv#readme",funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var fs=G((yh,Re)=>{"use strict";var ai=Vt("node:fs"),li=Vt("node:path"),qc=Vt("node:os"),Uc=Vt("node:crypto"),jc=us(),ps=jc.version,Bc=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function Qc(e){let t={},r=e.toString();r=r.replace(/\r\n?/mg,`
`);let n;for(;(n=Bc.exec(r))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),t[i]=o}return t}function Hc(e){let t=ms(e),r=j.configDotenv({path:t});if(!r.parsed){let s=new Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw s.code="MISSING_DATA",s}let n=ds(e).split(","),i=n.length,o;for(let s=0;s<i;s++)try{let a=n[s].trim(),l=Wc(r,a);o=j.decrypt(l.ciphertext,l.key);break}catch(a){if(s+1>=i)throw a}return j.parse(o)}function Gc(e){console.log(`[dotenv@${ps}][WARN] ${e}`)}function Wt(e){console.log(`[dotenv@${ps}][DEBUG] ${e}`)}function ds(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function Wc(e,t){let r;try{r=new URL(t)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let n=r.password;if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let i=r.searchParams.get("environment");if(!i){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let o=`DOTENV_VAULT_${i.toUpperCase()}`,s=e.parsed[o];if(!s){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:s,key:n}}function ms(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)ai.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=li.resolve(process.cwd(),".env.vault");return ai.existsSync(t)?t:null}function cs(e){return e[0]==="~"?li.join(qc.homedir(),e.slice(1)):e}function Jc(e){!!(e&&e.debug)&&Wt("Loading env from encrypted .env.vault");let r=j._parseVault(e),n=process.env;return e&&e.processEnv!=null&&(n=e.processEnv),j.populate(n,r,e),{parsed:r}}function Kc(e){let t=li.resolve(process.cwd(),".env"),r="utf8",n=!!(e&&e.debug);e&&e.encoding?r=e.encoding:n&&Wt("No encoding is specified. UTF-8 is used by default");let i=[t];if(e&&e.path)if(!Array.isArray(e.path))i=[cs(e.path)];else{i=[];for(let l of e.path)i.push(cs(l))}let o,s={};for(let l of i)try{let u=j.parse(ai.readFileSync(l,{encoding:r}));j.populate(s,u,e)}catch(u){n&&Wt(`Failed to load ${l} ${u.message}`),o=u}let a=process.env;return e&&e.processEnv!=null&&(a=e.processEnv),j.populate(a,s,e),o?{parsed:s,error:o}:{parsed:s}}function zc(e){if(ds(e).length===0)return j.configDotenv(e);let t=ms(e);return t?j._configVault(e):(Gc(`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`),j.configDotenv(e))}function Yc(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),o=n.subarray(-16);n=n.subarray(12,-16);try{let s=Uc.createDecipheriv("aes-256-gcm",r,i);return s.setAuthTag(o),`${s.update(n)}${s.final()}`}catch(s){let a=s instanceof RangeError,l=s.message==="Invalid key length",u=s.message==="Unsupported state or unable to authenticate data";if(a||l){let c=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw c.code="INVALID_DOTENV_KEY",c}else if(u){let c=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw c.code="DECRYPTION_FAILED",c}else throw s}}function Zc(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if(typeof t!="object"){let o=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw o.code="OBJECT_REQUIRED",o}for(let o of Object.keys(t))Object.prototype.hasOwnProperty.call(e,o)?(i===!0&&(e[o]=t[o]),n&&Wt(i===!0?`"${o}" is already defined and WAS overwritten`:`"${o}" is already defined and was NOT overwritten`)):e[o]=t[o]}var j={configDotenv:Kc,_configVault:Jc,_parseVault:Hc,config:zc,decrypt:Yc,parse:Qc,populate:Zc};Re.exports.configDotenv=j.configDotenv;Re.exports._configVault=j._configVault;Re.exports._parseVault=j._parseVault;Re.exports.config=j.config;Re.exports.decrypt=j.decrypt;Re.exports.parse=j.parse;Re.exports.populate=j.populate;Re.exports=j});var ws=G((Ah,Jr)=>{"use strict";Jr.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};Jr.exports.default=Jr.exports});var xi=G((tw,qs)=>{"use strict";qs.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,u,c,p,d,m,g,h,I,T,C,w,k=[];for(l=0;l<i;l++)k.push(l+1),k.push(t.charCodeAt(s+l));for(var de=k.length-1;a<o-3;)for(I=r.charCodeAt(s+(u=a)),T=r.charCodeAt(s+(c=a+1)),C=r.charCodeAt(s+(p=a+2)),w=r.charCodeAt(s+(d=a+3)),m=a+=4,l=0;l<de;l+=2)g=k[l],h=k[l+1],u=e(g,u,c,I,h),c=e(u,c,p,T,h),p=e(c,p,d,C,h),m=e(p,d,m,w,h),k[l]=m,d=p,p=c,c=u,u=g;for(;a<o;)for(I=r.charCodeAt(s+(u=a)),m=++a,l=0;l<de;l+=2)g=k[l],k[l]=m=e(g,u,m,I,k[l+1]),u=g;return m}}()});var Hs=Lo(()=>{"use strict"});var Gs=Lo(()=>{"use strict"});var ma=G((kx,Yd)=>{Yd.exports={name:"@prisma/engines-version",version:"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"81e4af48011447c3cc503a190e86995b66d2a28e"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var Ji=G(Ze=>{"use strict";Object.defineProperty(Ze,"__esModule",{value:!0});Ze.anumber=Wi;Ze.abytes=il;Ze.ahash=tf;Ze.aexists=rf;Ze.aoutput=nf;function Wi(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function ef(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function il(e,...t){if(!ef(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function tf(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Wi(e.outputLen),Wi(e.blockLen)}function rf(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function nf(e,t){il(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var Cl=G(P=>{"use strict";Object.defineProperty(P,"__esModule",{value:!0});P.add5L=P.add5H=P.add4H=P.add4L=P.add3H=P.add3L=P.rotlBL=P.rotlBH=P.rotlSL=P.rotlSH=P.rotr32L=P.rotr32H=P.rotrBL=P.rotrBH=P.rotrSL=P.rotrSH=P.shrSL=P.shrSH=P.toBig=void 0;P.fromBig=zi;P.split=ol;P.add=El;var kn=BigInt(2**32-1),Ki=BigInt(32);function zi(e,t=!1){return t?{h:Number(e&kn),l:Number(e>>Ki&kn)}:{h:Number(e>>Ki&kn)|0,l:Number(e&kn)|0}}function ol(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=zi(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var sl=(e,t)=>BigInt(e>>>0)<<Ki|BigInt(t>>>0);P.toBig=sl;var al=(e,t,r)=>e>>>r;P.shrSH=al;var ll=(e,t,r)=>e<<32-r|t>>>r;P.shrSL=ll;var ul=(e,t,r)=>e>>>r|t<<32-r;P.rotrSH=ul;var cl=(e,t,r)=>e<<32-r|t>>>r;P.rotrSL=cl;var pl=(e,t,r)=>e<<64-r|t>>>r-32;P.rotrBH=pl;var dl=(e,t,r)=>e>>>r-32|t<<64-r;P.rotrBL=dl;var ml=(e,t)=>t;P.rotr32H=ml;var fl=(e,t)=>e;P.rotr32L=fl;var gl=(e,t,r)=>e<<r|t>>>32-r;P.rotlSH=gl;var hl=(e,t,r)=>t<<r|e>>>32-r;P.rotlSL=hl;var yl=(e,t,r)=>t<<r-32|e>>>64-r;P.rotlBH=yl;var wl=(e,t,r)=>e<<r-32|t>>>64-r;P.rotlBL=wl;function El(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var xl=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);P.add3L=xl;var bl=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;P.add3H=bl;var Pl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);P.add4L=Pl;var vl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;P.add4H=vl;var Tl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);P.add5L=Tl;var Al=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;P.add5H=Al;var of={fromBig:zi,split:ol,toBig:sl,shrSH:al,shrSL:ll,rotrSH:ul,rotrSL:cl,rotrBH:pl,rotrBL:dl,rotr32H:ml,rotr32L:fl,rotlSH:gl,rotlSL:hl,rotlBH:yl,rotlBL:wl,add:El,add3L:xl,add3H:bl,add4L:Pl,add4H:vl,add5H:Al,add5L:Tl};P.default=of});var Sl=G(On=>{"use strict";Object.defineProperty(On,"__esModule",{value:!0});On.crypto=void 0;var Ue=Vt("node:crypto");On.crypto=Ue&&typeof Ue=="object"&&"webcrypto"in Ue?Ue.webcrypto:Ue&&typeof Ue=="object"&&"randomBytes"in Ue?Ue:void 0});var kl=G(R=>{"use strict";Object.defineProperty(R,"__esModule",{value:!0});R.Hash=R.nextTick=R.byteSwapIfBE=R.isLE=void 0;R.isBytes=sf;R.u8=af;R.u32=lf;R.createView=uf;R.rotr=cf;R.rotl=pf;R.byteSwap=Xi;R.byteSwap32=df;R.bytesToHex=ff;R.hexToBytes=gf;R.asyncLoop=yf;R.utf8ToBytes=Il;R.toBytes=Dn;R.concatBytes=wf;R.checkOpts=Ef;R.wrapConstructor=xf;R.wrapConstructorWithOpts=bf;R.wrapXOFConstructorWithOpts=Pf;R.randomBytes=vf;var At=Sl(),Zi=Ji();function sf(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function af(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function lf(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function uf(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function cf(e,t){return e<<32-t|e>>>t}function pf(e,t){return e<<t|e>>>32-t>>>0}R.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Xi(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}R.byteSwapIfBE=R.isLE?e=>e:e=>Xi(e);function df(e){for(let t=0;t<e.length;t++)e[t]=Xi(e[t])}var mf=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function ff(e){(0,Zi.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=mf[e[r]];return t}var Oe={_0:48,_9:57,A:65,F:70,a:97,f:102};function Rl(e){if(e>=Oe._0&&e<=Oe._9)return e-Oe._0;if(e>=Oe.A&&e<=Oe.F)return e-(Oe.A-10);if(e>=Oe.a&&e<=Oe.f)return e-(Oe.a-10)}function gf(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=Rl(e.charCodeAt(o)),a=Rl(e.charCodeAt(o+1));if(s===void 0||a===void 0){let l=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+l+'" at index '+o)}n[i]=s*16+a}return n}var hf=async()=>{};R.nextTick=hf;async function yf(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,R.nextTick)(),n+=o)}}function Il(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function Dn(e){return typeof e=="string"&&(e=Il(e)),(0,Zi.abytes)(e),e}function wf(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,Zi.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var Yi=class{clone(){return this._cloneInto()}};R.Hash=Yi;function Ef(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function xf(e){let t=n=>e().update(Dn(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function bf(e){let t=(n,i)=>e(i).update(Dn(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Pf(e){let t=(n,i)=>e(i).update(Dn(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function vf(e=32){if(At.crypto&&typeof At.crypto.getRandomValues=="function")return At.crypto.getRandomValues(new Uint8Array(e));if(At.crypto&&typeof At.crypto.randomBytes=="function")return At.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var $l=G(F=>{"use strict";Object.defineProperty(F,"__esModule",{value:!0});F.shake256=F.shake128=F.keccak_512=F.keccak_384=F.keccak_256=F.keccak_224=F.sha3_512=F.sha3_384=F.sha3_256=F.sha3_224=F.Keccak=void 0;F.keccakP=Ll;var Ct=Ji(),wr=Cl(),De=kl(),_l=[],Nl=[],Ml=[],Tf=BigInt(0),yr=BigInt(1),Af=BigInt(2),Cf=BigInt(7),Sf=BigInt(256),Rf=BigInt(113);for(let e=0,t=yr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],_l.push(2*(5*n+r)),Nl.push((e+1)*(e+2)/2%64);let i=Tf;for(let o=0;o<7;o++)t=(t<<yr^(t>>Cf)*Rf)%Sf,t&Af&&(i^=yr<<(yr<<BigInt(o))-yr);Ml.push(i)}var[If,kf]=(0,wr.split)(Ml,!0),Ol=(e,t,r)=>r>32?(0,wr.rotlBH)(e,t,r):(0,wr.rotlSH)(e,t,r),Dl=(e,t,r)=>r>32?(0,wr.rotlBL)(e,t,r):(0,wr.rotlSL)(e,t,r);function Ll(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,l=(s+2)%10,u=r[l],c=r[l+1],p=Ol(u,c,1)^r[a],d=Dl(u,c,1)^r[a+1];for(let m=0;m<50;m+=10)e[s+m]^=p,e[s+m+1]^=d}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=Nl[s],l=Ol(i,o,a),u=Dl(i,o,a),c=_l[s];i=e[c],o=e[c+1],e[c]=l,e[c+1]=u}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=If[n],e[1]^=kf[n]}r.fill(0)}var Er=class e extends De.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Ct.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,De.u32)(this.state)}keccak(){De.isLE||(0,De.byteSwap32)(this.state32),Ll(this.state32,this.rounds),De.isLE||(0,De.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,Ct.aexists)(this);let{blockLen:r,state:n}=this;t=(0,De.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,Ct.aexists)(this,!1),(0,Ct.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,Ct.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,Ct.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};F.Keccak=Er;var je=(e,t,r)=>(0,De.wrapConstructor)(()=>new Er(t,e,r));F.sha3_224=je(6,144,224/8);F.sha3_256=je(6,136,256/8);F.sha3_384=je(6,104,384/8);F.sha3_512=je(6,72,512/8);F.keccak_224=je(1,144,224/8);F.keccak_256=je(1,136,256/8);F.keccak_384=je(1,104,384/8);F.keccak_512=je(1,72,512/8);var Fl=(e,t,r)=>(0,De.wrapXOFConstructorWithOpts)((n={})=>new Er(t,e,n.dkLen===void 0?r:n.dkLen,!0));F.shake128=Fl(31,168,128/8);F.shake256=Fl(31,136,256/8)});var Gl=G((lP,Be)=>{"use strict";var{sha3_512:Of}=$l(),ql=24,xr=32,eo=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function Ul(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var jl=(e="")=>Ul(Of(e)).toString(36).slice(1),Vl=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),Df=e=>Vl[Math.floor(e()*Vl.length)],Bl=({globalObj:e=typeof global<"u"?global:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+eo(xr,t):eo(xr,t);return jl(n).substring(0,xr)},Ql=e=>()=>e++,_f=476782367,Hl=({random:e=Math.random,counter:t=Ql(Math.floor(e()*_f)),length:r=ql,fingerprint:n=Bl({random:e})}={})=>function(){let o=Df(e),s=Date.now().toString(36),a=t().toString(36),l=eo(r,e),u=`${s+l+a+n}`;return`${o+jl(u).substring(1,r)}`},Nf=Hl(),Mf=(e,{minLength:t=2,maxLength:r=xr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};Be.exports.getConstants=()=>({defaultLength:ql,bigLength:xr});Be.exports.init=Hl;Be.exports.createId=Nf;Be.exports.bufToBigInt=Ul;Be.exports.createCounter=Ql;Be.exports.createFingerprint=Bl;Be.exports.isCuid=Mf});var Wl=G((uP,br)=>{"use strict";var{createId:Lf,init:Ff,getConstants:$f,isCuid:Vf}=Gl();br.exports.createId=Lf;br.exports.init=Ff;br.exports.getConstants=$f;br.exports.isCuid=Vf});var Vo={};qt(Vo,{defineExtension:()=>Fo,getExtensionContext:()=>$o});function Fo(e){return typeof e=="function"?e:t=>t.$extends(e)}function $o(e){return e}var Uo={};qt(Uo,{validator:()=>qo});function qo(...e){return t=>t}var Qr={};qt(Qr,{$:()=>Go,bgBlack:()=>wc,bgBlue:()=>Pc,bgCyan:()=>Tc,bgGreen:()=>xc,bgMagenta:()=>vc,bgRed:()=>Ec,bgWhite:()=>Ac,bgYellow:()=>bc,black:()=>fc,blue:()=>We,bold:()=>X,cyan:()=>Se,dim:()=>He,gray:()=>jt,green:()=>Ut,grey:()=>yc,hidden:()=>dc,inverse:()=>pc,italic:()=>cc,magenta:()=>gc,red:()=>Ce,reset:()=>uc,strikethrough:()=>mc,underline:()=>ie,white:()=>hc,yellow:()=>Ge});var Xn,jo,Bo,Qo,Ho=!0;typeof process<"u"&&({FORCE_COLOR:Xn,NODE_DISABLE_COLORS:jo,NO_COLOR:Bo,TERM:Qo}=process.env||{},Ho=process.stdout&&process.stdout.isTTY);var Go={enabled:!jo&&Bo==null&&Qo!=="dumb"&&(Xn!=null&&Xn!=="0"||Ho)};function M(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!Go.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var uc=M(0,0),X=M(1,22),He=M(2,22),cc=M(3,23),ie=M(4,24),pc=M(7,27),dc=M(8,28),mc=M(9,29),fc=M(30,39),Ce=M(31,39),Ut=M(32,39),Ge=M(33,39),We=M(34,39),gc=M(35,39),Se=M(36,39),hc=M(37,39),jt=M(90,39),yc=M(90,39),wc=M(40,49),Ec=M(41,49),xc=M(42,49),bc=M(43,49),Pc=M(44,49),vc=M(45,49),Tc=M(46,49),Ac=M(47,49);var Cc=100,Wo=["green","yellow","blue","magenta","cyan","red"],Bt=[],Jo=Date.now(),Sc=0,ei=typeof process<"u"?process.env:{};globalThis.DEBUG??=ei.DEBUG??"";globalThis.DEBUG_COLORS??=ei.DEBUG_COLORS?ei.DEBUG_COLORS==="true":!0;var Qt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function Rc(e){let t={color:Wo[Sc++%Wo.length],enabled:Qt.enabled(e),namespace:e,log:Qt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Bt.push([o,...n]),Bt.length>Cc&&Bt.shift(),Qt.enabled(o)||i){let l=n.map(c=>typeof c=="string"?c:Ic(c)),u=`+${Date.now()-Jo}ms`;Jo=Date.now(),globalThis.DEBUG_COLORS?a(Qr[s](X(o)),...l,Qr[s](u)):a(o,...l,u)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var q=new Proxy(Rc,{get:(e,t)=>Qt[t],set:(e,t,r)=>Qt[t]=r});function Ic(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function Ko(e=7500){let t=Bt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function zo(){Bt.length=0}var Yo=q;var Oc=Zo(),ti=Oc.version;function lt(e){let t=Dc();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":_c(e))}function Dc(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function _c(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}var ts=ye(es(),1);function ri(e){let t=(0,ts.default)(e);if(t===0)return e;let r=new RegExp(`^[ \\t]{${t}}`,"gm");return e.replace(r,"")}var rs="prisma+postgres",Hr=`${rs}:`;function Gr(e){return e?.toString().startsWith(`${Hr}//`)??!1}function ni(e){if(!Gr(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")}var Gt={};qt(Gt,{error:()=>Lc,info:()=>Mc,log:()=>Nc,query:()=>Fc,should:()=>os,tags:()=>Ht,warn:()=>ii});var Ht={error:Ce("prisma:error"),warn:Ge("prisma:warn"),info:Se("prisma:info"),query:We("prisma:query")},os={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function Nc(...e){console.log(...e)}function ii(e,...t){os.warn()&&console.warn(`${Ht.warn} ${e}`,...t)}function Mc(e,...t){console.info(`${Ht.info} ${e}`,...t)}function Lc(e,...t){console.error(`${Ht.error} ${e}`,...t)}function Fc(e,...t){console.log(`${Ht.query} ${e}`,...t)}function me(e,t){throw new Error(t)}import Wr from"node:path";function si(e){return Wr.sep===Wr.posix.sep?e:e.split(Wr.sep).join(Wr.posix.sep)}var pi=ye(fs());import ui from"node:fs";import Jt from"node:path";function gs(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],u,c;if(l==="\\")c=a[0],u=c.replace("\\$","$");else{let p=a[2];c=a[0].substring(l.length),u=Object.hasOwnProperty.call(t,p)?t[p]:e.parsed[p]||"",u=r(u)}return o.replace(c,u)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let n in e.parsed)t[n]=e.parsed[n];return e}var ci=Yo("prisma:tryLoadEnv");function Kt({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=hs(e);r.conflictCheck!=="none"&&Xc(n,t,r.conflictCheck);let i=null;return ys(n?.path,t)||(i=hs(t)),!n&&!i&&ci("No Environment variables loaded"),i?.dotenvResult.error?console.error(Ce(X("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function Xc(e,t,r){let n=e?.dotenvResult.parsed,i=!ys(e?.path,t);if(n&&t&&i&&ui.existsSync(t)){let o=pi.default.parse(ui.readFileSync(t)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=Jt.relative(process.cwd(),e.path),l=Jt.relative(process.cwd(),t);if(r==="error"){let u=`There is a conflict between env var${s.length>1?"s":""} in ${ie(a)} and ${ie(l)}
Conflicting env vars:
${s.map(c=>`  ${X(c)}`).join(`
`)}

We suggest to move the contents of ${ie(l)} to ${ie(a)} to consolidate your env vars.
`;throw new Error(u)}else if(r==="warn"){let u=`Conflict for env var${s.length>1?"s":""} ${s.map(c=>X(c)).join(", ")} in ${ie(a)} and ${ie(l)}
Env vars from ${ie(l)} overwrite the ones from ${ie(a)}
      `;console.warn(`${Ge("warn(prisma)")} ${u}`)}}}}function hs(e){if(ep(e)){ci(`Environment variables loaded from ${e}`);let t=pi.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0});return{dotenvResult:gs(t),message:He(`Environment variables loaded from ${Jt.relative(process.cwd(),e)}`),path:e}}else ci(`Environment variables not found at ${e}`);return null}function ys(e,t){return e&&t&&Jt.resolve(e)===Jt.resolve(t)}function ep(e){return!!(e&&ui.existsSync(e))}function di(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function ut(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function mi(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}function b(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var Es=new Set,Kr=(e,t,...r)=>{Es.has(e)||(Es.add(e),ii(t,...r))};var O=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};b(O,"PrismaClientInitializationError");var Q=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};b(Q,"PrismaClientKnownRequestError");var oe=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};b(oe,"PrismaClientRustPanicError");var ee=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};b(ee,"PrismaClientUnknownRequestError");var te=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};b(te,"PrismaClientValidationError");var ct=9e15,Fe=1e9,fi="0123456789abcdef",Zr="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",Xr="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",gi={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-ct,maxE:ct,crypto:!1},Ts,Ie,x=!0,tn="[DecimalError] ",Le=tn+"Invalid argument: ",As=tn+"Precision limit exceeded",Cs=tn+"crypto unavailable",Ss="[object Decimal]",Y=Math.floor,B=Math.pow,tp=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,rp=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,np=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,Rs=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,fe=1e7,E=7,ip=9007199254740991,op=Zr.length-1,hi=Xr.length-1,f={toStringTag:Ss};f.absoluteValue=f.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),y(e)};f.ceil=function(){return y(new this.constructor(this),this.e+1,2)};f.clampedTo=f.clamp=function(e,t){var r,n=this,i=n.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(Le+t);return r=n.cmp(e),r<0?e:n.cmp(t)>0?t:new i(n)};f.comparedTo=f.cmp=function(e){var t,r,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,u=e.s;if(!s||!a)return!l||!u?NaN:l!==u?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-u:0;if(l!==u)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};f.cosine=f.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=sp(n,_s(n,r)),n.precision=e,n.rounding=t,y(Ie==2||Ie==3?r.neg():r,e,t,!0)):new n(1):new n(NaN)};f.cubeRoot=f.cbrt=function(){var e,t,r,n,i,o,s,a,l,u,c=this,p=c.constructor;if(!c.isFinite()||c.isZero())return new p(c);for(x=!1,o=c.s*B(c.s*c,1/3),!o||Math.abs(o)==1/0?(r=W(c.d),e=c.e,(o=(e-r.length+1)%3)&&(r+=o==1||o==-2?"0":"00"),o=B(r,1/3),e=Y((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?r="5e"+e:(r=o.toExponential(),r=r.slice(0,r.indexOf("e")+1)+e),n=new p(r),n.s=c.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),u=l.plus(c),n=N(u.plus(c).times(a),u.plus(l),s+2,1),W(a.d).slice(0,s)===(r=W(n.d)).slice(0,s))if(r=r.slice(s-3,s+1),r=="9999"||!i&&r=="4999"){if(!i&&(y(a,e+1,0),a.times(a).times(a).eq(c))){n=a;break}s+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(y(n,e+1,1),t=!n.times(n).times(n).eq(c));break}return x=!0,y(n,e,p.rounding,t)};f.decimalPlaces=f.dp=function(){var e,t=this.d,r=NaN;if(t){if(e=t.length-1,r=(e-Y(this.e/E))*E,e=t[e],e)for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r};f.dividedBy=f.div=function(e){return N(this,new this.constructor(e))};f.dividedToIntegerBy=f.divToInt=function(e){var t=this,r=t.constructor;return y(N(t,new r(e),0,1,1),r.precision,r.rounding)};f.equals=f.eq=function(e){return this.cmp(e)===0};f.floor=function(){return y(new this.constructor(this),this.e+1,3)};f.greaterThan=f.gt=function(e){return this.cmp(e)>0};f.greaterThanOrEqualTo=f.gte=function(e){var t=this.cmp(e);return t==1||t===0};f.hyperbolicCosine=f.cosh=function(){var e,t,r,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;r=s.precision,n=s.rounding,s.precision=r+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),t=(1/nn(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),o=pt(s,1,o.times(t),new s(1),!0);for(var l,u=e,c=new s(8);u--;)l=o.times(o),o=a.minus(l.times(c.minus(l.times(c))));return y(o,s.precision=r,s.rounding=n,!0)};f.hyperbolicSine=f.sinh=function(){var e,t,r,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,r=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=pt(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/nn(5,e)),i=pt(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),u=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(u))))}return o.precision=t,o.rounding=r,y(i,t,r,!0)};f.hyperbolicTangent=f.tanh=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+7,n.rounding=1,N(r.sinh(),r.cosh(),n.precision=e,n.rounding=t)):new n(r.s)};f.inverseCosine=f.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return r!==-1?r===0?e.isNeg()?we(t,n,i):new t(0):new t(NaN):e.isZero()?we(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))};f.inverseHyperbolicCosine=f.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,x=!1,r=r.times(r).minus(1).sqrt().plus(r),x=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)};f.inverseHyperbolicSine=f.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,x=!1,r=r.times(r).plus(1).sqrt().plus(r),x=!0,n.precision=e,n.rounding=t,r.ln())};f.inverseHyperbolicTangent=f.atanh=function(){var e,t,r,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?y(new o(i),e,t,!0):(o.precision=r=n-i.e,i=N(i.plus(1),new o(1).minus(i),r+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)};f.inverseSine=f.asin=function(){var e,t,r,n,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),r=o.precision,n=o.rounding,t!==-1?t===0?(e=we(o,r+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=r+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=r,o.rounding=n,i.times(2)))};f.inverseTangent=f.atan=function(){var e,t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding;if(u.isFinite()){if(u.isZero())return new c(u);if(u.abs().eq(1)&&p+4<=hi)return s=we(c,p+4,d).times(.25),s.s=u.s,s}else{if(!u.s)return new c(NaN);if(p+4<=hi)return s=we(c,p+4,d).times(.5),s.s=u.s,s}for(c.precision=a=p+10,c.rounding=1,r=Math.min(28,a/E+2|0),e=r;e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(x=!1,t=Math.ceil(a/E),n=1,l=u.times(u),s=new c(u),i=u;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[t]!==void 0)for(e=t;s.d[e]===o.d[e]&&e--;);return r&&(s=s.times(2<<r-1)),x=!0,y(s,c.precision=p,c.rounding=d,!0)};f.isFinite=function(){return!!this.d};f.isInteger=f.isInt=function(){return!!this.d&&Y(this.e/E)>this.d.length-2};f.isNaN=function(){return!this.s};f.isNegative=f.isNeg=function(){return this.s<0};f.isPositive=f.isPos=function(){return this.s>0};f.isZero=function(){return!!this.d&&this.d[0]===0};f.lessThan=f.lt=function(e){return this.cmp(e)<0};f.lessThanOrEqualTo=f.lte=function(e){return this.cmp(e)<1};f.logarithm=f.log=function(e){var t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding,m=5;if(e==null)e=new c(10),t=!0;else{if(e=new c(e),r=e.d,e.s<0||!r||!r[0]||e.eq(1))return new c(NaN);t=e.eq(10)}if(r=u.d,u.s<0||!r||!r[0]||u.eq(1))return new c(r&&!r[0]?-1/0:u.s!=1?NaN:r?0:1/0);if(t)if(r.length>1)o=!0;else{for(i=r[0];i%10===0;)i/=10;o=i!==1}if(x=!1,a=p+m,s=Me(u,a),n=t?en(c,a+10):Me(e,a),l=N(s,n,a,1),zt(l.d,i=p,d))do if(a+=10,s=Me(u,a),n=t?en(c,a+10):Me(e,a),l=N(s,n,a,1),!o){+W(l.d).slice(i+1,i+15)+1==1e14&&(l=y(l,p+1,0));break}while(zt(l.d,i+=10,d));return x=!0,y(l,p,d)};f.minus=f.sub=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,m=this,g=m.constructor;if(e=new g(e),!m.d||!e.d)return!m.s||!e.s?e=new g(NaN):m.d?e.s=-e.s:e=new g(e.d||m.s!==e.s?m:NaN),e;if(m.s!=e.s)return e.s=-e.s,m.plus(e);if(u=m.d,d=e.d,a=g.precision,l=g.rounding,!u[0]||!d[0]){if(d[0])e.s=-e.s;else if(u[0])e=new g(m);else return new g(l===3?-0:0);return x?y(e,a,l):e}if(r=Y(e.e/E),c=Y(m.e/E),u=u.slice(),o=c-r,o){for(p=o<0,p?(t=u,o=-o,s=d.length):(t=d,r=c,s=u.length),n=Math.max(Math.ceil(a/E),s)+2,o>n&&(o=n,t.length=1),t.reverse(),n=o;n--;)t.push(0);t.reverse()}else{for(n=u.length,s=d.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(u[n]!=d[n]){p=u[n]<d[n];break}o=0}for(p&&(t=u,u=d,d=t,e.s=-e.s),s=u.length,n=d.length-s;n>0;--n)u[s++]=0;for(n=d.length;n>o;){if(u[--n]<d[n]){for(i=n;i&&u[--i]===0;)u[i]=fe-1;--u[i],u[n]+=fe}u[n]-=d[n]}for(;u[--s]===0;)u.pop();for(;u[0]===0;u.shift())--r;return u[0]?(e.d=u,e.e=rn(u,r),x?y(e,a,l):e):new g(l===3?-0:0)};f.modulo=f.mod=function(e){var t,r=this,n=r.constructor;return e=new n(e),!r.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||r.d&&!r.d[0]?y(new n(r),n.precision,n.rounding):(x=!1,n.modulo==9?(t=N(r,e.abs(),0,3,1),t.s*=e.s):t=N(r,e,0,n.modulo,1),t=t.times(e),x=!0,r.minus(t))};f.naturalExponential=f.exp=function(){return yi(this)};f.naturalLogarithm=f.ln=function(){return Me(this)};f.negated=f.neg=function(){var e=new this.constructor(this);return e.s=-e.s,y(e)};f.plus=f.add=function(e){var t,r,n,i,o,s,a,l,u,c,p=this,d=p.constructor;if(e=new d(e),!p.d||!e.d)return!p.s||!e.s?e=new d(NaN):p.d||(e=new d(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(u=p.d,c=e.d,a=d.precision,l=d.rounding,!u[0]||!c[0])return c[0]||(e=new d(p)),x?y(e,a,l):e;if(o=Y(p.e/E),n=Y(e.e/E),u=u.slice(),i=o-n,i){for(i<0?(r=u,i=-i,s=c.length):(r=c,n=o,s=u.length),o=Math.ceil(a/E),s=o>s?o+1:s+1,i>s&&(i=s,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for(s=u.length,i=c.length,s-i<0&&(i=s,r=c,c=u,u=r),t=0;i;)t=(u[--i]=u[i]+c[i]+t)/fe|0,u[i]%=fe;for(t&&(u.unshift(t),++n),s=u.length;u[--s]==0;)u.pop();return e.d=u,e.e=rn(u,n),x?y(e,a,l):e};f.precision=f.sd=function(e){var t,r=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Le+e);return r.d?(t=Is(r.d),e&&r.e+1>t&&(t=r.e+1)):t=NaN,t};f.round=function(){var e=this,t=e.constructor;return y(new t(e),e.e+1,t.rounding)};f.sine=f.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=lp(n,_s(n,r)),n.precision=e,n.rounding=t,y(Ie>2?r.neg():r,e,t,!0)):new n(NaN)};f.squareRoot=f.sqrt=function(){var e,t,r,n,i,o,s=this,a=s.d,l=s.e,u=s.s,c=s.constructor;if(u!==1||!a||!a[0])return new c(!u||u<0&&(!a||a[0])?NaN:a?s:1/0);for(x=!1,u=Math.sqrt(+s),u==0||u==1/0?(t=W(a),(t.length+l)%2==0&&(t+="0"),u=Math.sqrt(t),l=Y((l+1)/2)-(l<0||l%2),u==1/0?t="5e"+l:(t=u.toExponential(),t=t.slice(0,t.indexOf("e")+1)+l),n=new c(t)):n=new c(u.toString()),r=(l=c.precision)+3;;)if(o=n,n=o.plus(N(s,o,r+2,1)).times(.5),W(o.d).slice(0,r)===(t=W(n.d)).slice(0,r))if(t=t.slice(r-3,r+1),t=="9999"||!i&&t=="4999"){if(!i&&(y(o,l+1,0),o.times(o).eq(s))){n=o;break}r+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(y(n,l+1,1),e=!n.times(n).eq(s));break}return x=!0,y(n,l,c.rounding,e)};f.tangent=f.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,r=r.sin(),r.s=1,r=N(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,y(Ie==2||Ie==4?r.neg():r,e,t,!0)):new n(NaN)};f.times=f.mul=function(e){var t,r,n,i,o,s,a,l,u,c=this,p=c.constructor,d=c.d,m=(e=new p(e)).d;if(e.s*=c.s,!d||!d[0]||!m||!m[0])return new p(!e.s||d&&!d[0]&&!m||m&&!m[0]&&!d?NaN:!d||!m?e.s/0:e.s*0);for(r=Y(c.e/E)+Y(e.e/E),l=d.length,u=m.length,l<u&&(o=d,d=m,m=o,s=l,l=u,u=s),o=[],s=l+u,n=s;n--;)o.push(0);for(n=u;--n>=0;){for(t=0,i=l+n;i>n;)a=o[i]+m[n]*d[i-n-1]+t,o[i--]=a%fe|0,t=a/fe|0;o[i]=(o[i]+t)%fe|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=rn(o,r),x?y(e,p.precision,p.rounding):e};f.toBinary=function(e,t){return wi(this,2,e,t)};f.toDecimalPlaces=f.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(se(e,0,Fe),t===void 0?t=n.rounding:se(t,0,8),y(r,e+r.e+1,t))};f.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Ee(n,!0):(se(e,0,Fe),t===void 0?t=i.rounding:se(t,0,8),n=y(new i(n),e+1,t),r=Ee(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?r=Ee(i):(se(e,0,Fe),t===void 0?t=o.rounding:se(t,0,8),n=y(new o(i),e+i.e+1,t),r=Ee(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+r:r};f.toFraction=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,m=this,g=m.d,h=m.constructor;if(!g)return new h(m);if(u=r=new h(1),n=l=new h(0),t=new h(n),o=t.e=Is(g)-m.e-1,s=o%E,t.d[0]=B(10,s<0?E+s:s),e==null)e=o>0?t:u;else{if(a=new h(e),!a.isInt()||a.lt(u))throw Error(Le+a);e=a.gt(t)?o>0?t:u:a}for(x=!1,a=new h(W(g)),c=h.precision,h.precision=o=g.length*E*2;p=N(a,t,0,1,1),i=r.plus(p.times(n)),i.cmp(e)!=1;)r=n,n=i,i=u,u=l.plus(p.times(i)),l=i,i=t,t=a.minus(p.times(i)),a=i;return i=N(e.minus(r),n,0,1,1),l=l.plus(i.times(u)),r=r.plus(i.times(n)),l.s=u.s=m.s,d=N(u,n,o,1).minus(m).abs().cmp(N(l,r,o,1).minus(m).abs())<1?[u,n]:[l,r],h.precision=c,x=!0,d};f.toHexadecimal=f.toHex=function(e,t){return wi(this,16,e,t)};f.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),e==null){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),t===void 0?t=n.rounding:se(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(x=!1,r=N(r,e,0,t,1).times(e),x=!0,y(r)):(e.s=r.s,r=e),r};f.toNumber=function(){return+this};f.toOctal=function(e,t){return wi(this,8,e,t)};f.toPower=f.pow=function(e){var t,r,n,i,o,s,a=this,l=a.constructor,u=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l(B(+a,u));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return y(a,n,o);if(t=Y(e.e/E),t>=e.d.length-1&&(r=u<0?-u:u)<=ip)return i=ks(l,a,r,n),e.s<0?new l(1).div(i):y(i,n,o);if(s=a.s,s<0){if(t<e.d.length-1)return new l(NaN);if((e.d[t]&1)==0&&(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return r=B(+a,u),t=r==0||!isFinite(r)?Y(u*(Math.log("0."+W(a.d))/Math.LN10+a.e+1)):new l(r+"").e,t>l.maxE+1||t<l.minE-1?new l(t>0?s/0:0):(x=!1,l.rounding=a.s=1,r=Math.min(12,(t+"").length),i=yi(e.times(Me(a,n+r)),n),i.d&&(i=y(i,n+5,1),zt(i.d,n,o)&&(t=n+10,i=y(yi(e.times(Me(a,t+r)),t),t+5,1),+W(i.d).slice(n+1,n+15)+1==1e14&&(i=y(i,n+1,0)))),i.s=s,x=!0,l.rounding=o,y(i,n,o))};f.toPrecision=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Ee(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(se(e,1,Fe),t===void 0?t=i.rounding:se(t,0,8),n=y(new i(n),e,t),r=Ee(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toSignificantDigits=f.toSD=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(se(e,1,Fe),t===void 0?t=n.rounding:se(t,0,8)),y(new n(r),e,t)};f.toString=function(){var e=this,t=e.constructor,r=Ee(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+r:r};f.truncated=f.trunc=function(){return y(new this.constructor(this),this.e+1,1)};f.valueOf=f.toJSON=function(){var e=this,t=e.constructor,r=Ee(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+r:r};function W(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=E-n.length,r&&(o+=Ne(r)),o+=n;s=e[t],n=s+"",r=E-n.length,r&&(o+=Ne(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function se(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Le+e)}function zt(e,t,r,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=E,i=0):(i=Math.ceil((t+1)/E),t%=E),o=B(10,E-t),a=e[i]%o|0,n==null?t<3?(t==0?a=a/100|0:t==1&&(a=a/10|0),s=r<4&&a==99999||r>3&&a==49999||a==5e4||a==0):s=(r<4&&a+1==o||r>3&&a+1==o/2)&&(e[i+1]/o/100|0)==B(10,t-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:t<4?(t==0?a=a/1e3|0:t==1?a=a/100|0:t==2&&(a=a/10|0),s=(n||r<4)&&a==9999||!n&&r>3&&a==4999):s=((n||r<4)&&a+1==o||!n&&r>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==B(10,t-3)-1,s}function zr(e,t,r){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=t;for(i[0]+=fi.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>r-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/r|0,i[n]%=r)}return i.reverse()}function sp(e,t){var r,n,i;if(t.isZero())return t;n=t.d.length,n<32?(r=Math.ceil(n/3),i=(1/nn(4,r)).toString()):(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=pt(e,1,t.times(i),new e(1));for(var o=r;o--;){var s=t.times(t);t=s.times(s).minus(s).times(8).plus(1)}return e.precision-=r,t}var N=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function t(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function r(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var u,c,p,d,m,g,h,I,T,C,w,k,de,le,$t,U,ne,Ae,K,at,Br=n.constructor,Yn=n.s==i.s?1:-1,z=n.d,_=i.d;if(!z||!z[0]||!_||!_[0])return new Br(!n.s||!i.s||(z?_&&z[0]==_[0]:!_)?NaN:z&&z[0]==0||!_?Yn*0:Yn/0);for(l?(m=1,c=n.e-i.e):(l=fe,m=E,c=Y(n.e/m)-Y(i.e/m)),K=_.length,ne=z.length,T=new Br(Yn),C=T.d=[],p=0;_[p]==(z[p]||0);p++);if(_[p]>(z[p]||0)&&c--,o==null?(le=o=Br.precision,s=Br.rounding):a?le=o+(n.e-i.e)+1:le=o,le<0)C.push(1),g=!0;else{if(le=le/m+2|0,p=0,K==1){for(d=0,_=_[0],le++;(p<ne||d)&&le--;p++)$t=d*l+(z[p]||0),C[p]=$t/_|0,d=$t%_|0;g=d||p<ne}else{for(d=l/(_[0]+1)|0,d>1&&(_=e(_,d,l),z=e(z,d,l),K=_.length,ne=z.length),U=K,w=z.slice(0,K),k=w.length;k<K;)w[k++]=0;at=_.slice(),at.unshift(0),Ae=_[0],_[1]>=l/2&&++Ae;do d=0,u=t(_,w,K,k),u<0?(de=w[0],K!=k&&(de=de*l+(w[1]||0)),d=de/Ae|0,d>1?(d>=l&&(d=l-1),h=e(_,d,l),I=h.length,k=w.length,u=t(h,w,I,k),u==1&&(d--,r(h,K<I?at:_,I,l))):(d==0&&(u=d=1),h=_.slice()),I=h.length,I<k&&h.unshift(0),r(w,h,k,l),u==-1&&(k=w.length,u=t(_,w,K,k),u<1&&(d++,r(w,K<k?at:_,k,l))),k=w.length):u===0&&(d++,w=[0]),C[p++]=d,u&&w[0]?w[k++]=z[U]||0:(w=[z[U]],k=1);while((U++<ne||w[0]!==void 0)&&le--);g=w[0]!==void 0}C[0]||C.shift()}if(m==1)T.e=c,Ts=g;else{for(p=1,d=C[0];d>=10;d/=10)p++;T.e=p+c*m-1,y(T,a?o+T.e+1:o,s,g)}return T}}();function y(e,t,r,n){var i,o,s,a,l,u,c,p,d,m=e.constructor;e:if(t!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=t-i,o<0)o+=E,s=t,c=p[d=0],l=c/B(10,i-s-1)%10|0;else if(d=Math.ceil((o+1)/E),a=p.length,d>=a)if(n){for(;a++<=d;)p.push(0);c=l=0,i=1,o%=E,s=o-E+1}else break e;else{for(c=a=p[d],i=1;a>=10;a/=10)i++;o%=E,s=o-E+i,l=s<0?0:c/B(10,i-s-1)%10|0}if(n=n||t<0||p[d+1]!==void 0||(s<0?c:c%B(10,i-s-1)),u=r<4?(l||n)&&(r==0||r==(e.s<0?3:2)):l>5||l==5&&(r==4||n||r==6&&(o>0?s>0?c/B(10,i-s):0:p[d-1])%10&1||r==(e.s<0?8:7)),t<1||!p[0])return p.length=0,u?(t-=e.e+1,p[0]=B(10,(E-t%E)%E),e.e=-t||0):p[0]=e.e=0,e;if(o==0?(p.length=d,a=1,d--):(p.length=d+1,a=B(10,E-o),p[d]=s>0?(c/B(10,i-s)%B(10,s)|0)*a:0),u)for(;;)if(d==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==fe&&(p[0]=1));break}else{if(p[d]+=a,p[d]!=fe)break;p[d--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return x&&(e.e>m.maxE?(e.d=null,e.e=NaN):e.e<m.minE&&(e.e=0,e.d=[0])),e}function Ee(e,t,r){if(!e.isFinite())return Ds(e);var n,i=e.e,o=W(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ne(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+Ne(-i-1)+o,r&&(n=r-s)>0&&(o+=Ne(n))):i>=s?(o+=Ne(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ne(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ne(n))),o}function rn(e,t){var r=e[0];for(t*=E;r>=10;r/=10)t++;return t}function en(e,t,r){if(t>op)throw x=!0,r&&(e.precision=r),Error(As);return y(new e(Zr),t,1,!0)}function we(e,t,r){if(t>hi)throw Error(As);return y(new e(Xr),t,r,!0)}function Is(e){var t=e.length-1,r=t*E+1;if(t=e[t],t){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function Ne(e){for(var t="";e--;)t+="0";return t}function ks(e,t,r,n){var i,o=new e(1),s=Math.ceil(n/E+4);for(x=!1;;){if(r%2&&(o=o.times(t),Ps(o.d,s)&&(i=!0)),r=Y(r/2),r===0){r=o.d.length-1,i&&o.d[r]===0&&++o.d[r];break}t=t.times(t),Ps(t.d,s)}return x=!0,o}function bs(e){return e.d[e.d.length-1]&1}function Os(e,t,r){for(var n,i,o=new e(t[0]),s=0;++s<t.length;){if(i=new e(t[s]),!i.s){o=i;break}n=o.cmp(i),(n===r||n===0&&o.s===r)&&(o=i)}return o}function yi(e,t){var r,n,i,o,s,a,l,u=0,c=0,p=0,d=e.constructor,m=d.rounding,g=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(t==null?(x=!1,l=g):l=t,a=new d(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log(B(2,p))/Math.LN10*2+5|0,l+=n,r=o=s=new d(1),d.precision=l;;){if(o=y(o.times(e),l,1),r=r.times(++c),a=s.plus(N(o,r,l,1)),W(a.d).slice(0,l)===W(s.d).slice(0,l)){for(i=p;i--;)s=y(s.times(s),l,1);if(t==null)if(u<3&&zt(s.d,l-n,m,u))d.precision=l+=10,r=o=a=new d(1),c=0,u++;else return y(s,d.precision=g,m,x=!0);else return d.precision=g,s}s=a}}function Me(e,t){var r,n,i,o,s,a,l,u,c,p,d,m=1,g=10,h=e,I=h.d,T=h.constructor,C=T.rounding,w=T.precision;if(h.s<0||!I||!I[0]||!h.e&&I[0]==1&&I.length==1)return new T(I&&!I[0]?-1/0:h.s!=1?NaN:I?0:h);if(t==null?(x=!1,c=w):c=t,T.precision=c+=g,r=W(I),n=r.charAt(0),Math.abs(o=h.e)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=W(h.d),n=r.charAt(0),m++;o=h.e,n>1?(h=new T("0."+r),o++):h=new T(n+"."+r.slice(1))}else return u=en(T,c+2,w).times(o+""),h=Me(new T(n+"."+r.slice(1)),c-g).plus(u),T.precision=w,t==null?y(h,w,C,x=!0):h;for(p=h,l=s=h=N(h.minus(1),h.plus(1),c,1),d=y(h.times(h),c,1),i=3;;){if(s=y(s.times(d),c,1),u=l.plus(N(s,new T(i),c,1)),W(u.d).slice(0,c)===W(l.d).slice(0,c))if(l=l.times(2),o!==0&&(l=l.plus(en(T,c+2,w).times(o+""))),l=N(l,new T(m),c,1),t==null)if(zt(l.d,c-g,C,a))T.precision=c+=g,u=s=h=N(p.minus(1),p.plus(1),c,1),d=y(h.times(h),c,1),i=a=1;else return y(l,T.precision=w,C,x=!0);else return T.precision=w,l;l=u,i+=2}}function Ds(e){return String(e.s*e.s/0)}function Yr(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;n++);for(i=t.length;t.charCodeAt(i-1)===48;--i);if(t=t.slice(n,i),t){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%E,r<0&&(n+=E),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=E;n<i;)e.d.push(+t.slice(n,n+=E));t=t.slice(n),n=E-t.length}else n-=i;for(;n--;)t+="0";e.d.push(+t),x&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function ap(e,t){var r,n,i,o,s,a,l,u,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),Rs.test(t))return Yr(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(rp.test(t))r=16,t=t.toLowerCase();else if(tp.test(t))r=2;else if(np.test(t))r=8;else throw Error(Le+t);for(o=t.search(/p/i),o>0?(l=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),o=t.indexOf("."),s=o>=0,n=e.constructor,s&&(t=t.replace(".",""),a=t.length,o=a-o,i=ks(n,new n(r),o,o*2)),u=zr(t,r,fe),c=u.length-1,o=c;u[o]===0;--o)u.pop();return o<0?new n(e.s*0):(e.e=rn(u,c),e.d=u,x=!1,s&&(e=N(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?B(2,l):Je.pow(2,l))),x=!0,e)}function lp(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:pt(e,2,t,t);r=1.4*Math.sqrt(n),r=r>16?16:r|0,t=t.times(1/nn(5,r)),t=pt(e,2,t,t);for(var i,o=new e(5),s=new e(16),a=new e(20);r--;)i=t.times(t),t=t.times(o.plus(i.times(s.times(i).minus(a))));return t}function pt(e,t,r,n,i){var o,s,a,l,u=1,c=e.precision,p=Math.ceil(c/E);for(x=!1,l=r.times(r),a=new e(n);;){if(s=N(a.times(l),new e(t++*t++),c,1),a=i?n.plus(s):n.minus(s),n=N(s.times(l),new e(t++*t++),c,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,u++}return x=!0,s.d.length=p+1,s}function nn(e,t){for(var r=e;--t;)r*=e;return r}function _s(e,t){var r,n=t.s<0,i=we(e,e.precision,1),o=i.times(.5);if(t=t.abs(),t.lte(o))return Ie=n?4:1,t;if(r=t.divToInt(i),r.isZero())Ie=n?3:2;else{if(t=t.minus(r.times(i)),t.lte(o))return Ie=bs(r)?n?2:3:n?4:1,t;Ie=bs(r)?n?1:4:n?3:2}return t.minus(i).abs()}function wi(e,t,r,n){var i,o,s,a,l,u,c,p,d,m=e.constructor,g=r!==void 0;if(g?(se(r,1,Fe),n===void 0?n=m.rounding:se(n,0,8)):(r=m.precision,n=m.rounding),!e.isFinite())c=Ds(e);else{for(c=Ee(e),s=c.indexOf("."),g?(i=2,t==16?r=r*4-3:t==8&&(r=r*3-2)):i=t,s>=0&&(c=c.replace(".",""),d=new m(1),d.e=c.length-s,d.d=zr(Ee(d),10,i),d.e=d.d.length),p=zr(c,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])c=g?"0p+0":"0";else{if(s<0?o--:(e=new m(e),e.d=p,e.e=o,e=N(e,d,r,n,0,i),p=e.d,o=e.e,u=Ts),s=p[r],a=i/2,u=u||p[r+1]!==void 0,u=n<4?(s!==void 0||u)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||u||n===6&&p[r-1]&1||n===(e.s<0?8:7)),p.length=r,u)for(;++p[--r]>i-1;)p[r]=0,r||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,c="";s<l;s++)c+=fi.charAt(p[s]);if(g){if(l>1)if(t==16||t==8){for(s=t==16?4:3,--l;l%s;l++)c+="0";for(p=zr(c,i,t),l=p.length;!p[l-1];--l);for(s=1,c="1.";s<l;s++)c+=fi.charAt(p[s])}else c=c.charAt(0)+"."+c.slice(1);c=c+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)c="0"+c;c="0."+c}else if(++o>l)for(o-=l;o--;)c+="0";else o<l&&(c=c.slice(0,o)+"."+c.slice(o))}c=(t==16?"0x":t==2?"0b":t==8?"0o":"")+c}return e.s<0?"-"+c:c}function Ps(e,t){if(e.length>t)return e.length=t,!0}function up(e){return new this(e).abs()}function cp(e){return new this(e).acos()}function pp(e){return new this(e).acosh()}function dp(e,t){return new this(e).plus(t)}function mp(e){return new this(e).asin()}function fp(e){return new this(e).asinh()}function gp(e){return new this(e).atan()}function hp(e){return new this(e).atanh()}function yp(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,o=n+4;return!e.s||!t.s?r=new this(NaN):!e.d&&!t.d?(r=we(this,o,1).times(t.s>0?.25:.75),r.s=e.s):!t.d||e.isZero()?(r=t.s<0?we(this,n,i):new this(0),r.s=e.s):!e.d||t.isZero()?(r=we(this,o,1).times(.5),r.s=e.s):t.s<0?(this.precision=o,this.rounding=1,r=this.atan(N(e,t,o,1)),t=we(this,o,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(N(e,t,o,1)),r}function wp(e){return new this(e).cbrt()}function Ep(e){return y(e=new this(e),e.e+1,2)}function xp(e,t,r){return new this(e).clamp(t,r)}function bp(e){if(!e||typeof e!="object")throw Error(tn+"Object expected");var t,r,n,i=e.defaults===!0,o=["precision",1,Fe,"rounding",0,8,"toExpNeg",-ct,0,"toExpPos",0,ct,"maxE",0,ct,"minE",-ct,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(r=o[t],i&&(this[r]=gi[r]),(n=e[r])!==void 0)if(Y(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(Le+r+": "+n);if(r="crypto",i&&(this[r]=gi[r]),(n=e[r])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(Cs);else this[r]=!1;else throw Error(Le+r+": "+n);return this}function Pp(e){return new this(e).cos()}function vp(e){return new this(e).cosh()}function Ns(e){var t,r,n;function i(o){var s,a,l,u=this;if(!(u instanceof i))return new i(o);if(u.constructor=i,vs(o)){u.s=o.s,x?!o.d||o.e>i.maxE?(u.e=NaN,u.d=null):o.e<i.minE?(u.e=0,u.d=[0]):(u.e=o.e,u.d=o.d.slice()):(u.e=o.e,u.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){u.s=1/o<0?-1:1,u.e=0,u.d=[0];return}if(o<0?(o=-o,u.s=-1):u.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;x?s>i.maxE?(u.e=NaN,u.d=null):s<i.minE?(u.e=0,u.d=[0]):(u.e=s,u.d=[o]):(u.e=s,u.d=[o]);return}if(o*0!==0){o||(u.s=NaN),u.e=NaN,u.d=null;return}return Yr(u,o.toString())}if(l==="string")return(a=o.charCodeAt(0))===45?(o=o.slice(1),u.s=-1):(a===43&&(o=o.slice(1)),u.s=1),Rs.test(o)?Yr(u,o):ap(u,o);if(l==="bigint")return o<0?(o=-o,u.s=-1):u.s=1,Yr(u,o.toString());throw Error(Le+o)}if(i.prototype=f,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=bp,i.clone=Ns,i.isDecimal=vs,i.abs=up,i.acos=cp,i.acosh=pp,i.add=dp,i.asin=mp,i.asinh=fp,i.atan=gp,i.atanh=hp,i.atan2=yp,i.cbrt=wp,i.ceil=Ep,i.clamp=xp,i.cos=Pp,i.cosh=vp,i.div=Tp,i.exp=Ap,i.floor=Cp,i.hypot=Sp,i.ln=Rp,i.log=Ip,i.log10=Op,i.log2=kp,i.max=Dp,i.min=_p,i.mod=Np,i.mul=Mp,i.pow=Lp,i.random=Fp,i.round=$p,i.sign=Vp,i.sin=qp,i.sinh=Up,i.sqrt=jp,i.sub=Bp,i.sum=Qp,i.tan=Hp,i.tanh=Gp,i.trunc=Wp,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function Tp(e,t){return new this(e).div(t)}function Ap(e){return new this(e).exp()}function Cp(e){return y(e=new this(e),e.e+1,3)}function Sp(){var e,t,r=new this(0);for(x=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return x=!0,new this(1/0);r=t}return x=!0,r.sqrt()}function vs(e){return e instanceof Je||e&&e.toStringTag===Ss||!1}function Rp(e){return new this(e).ln()}function Ip(e,t){return new this(e).log(t)}function kp(e){return new this(e).log(2)}function Op(e){return new this(e).log(10)}function Dp(){return Os(this,arguments,-1)}function _p(){return Os(this,arguments,1)}function Np(e,t){return new this(e).mod(t)}function Mp(e,t){return new this(e).mul(t)}function Lp(e,t){return new this(e).pow(t)}function Fp(e){var t,r,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:se(e,1,Fe),n=Math.ceil(e/E),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));o<n;)i=t[o],i>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);o<n;)i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((t[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error(Cs);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=E,n&&e&&(i=B(10,E-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)r=0,a=[0];else{for(r=-1;a[0]===0;r-=E)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<E&&(r-=E-n)}return s.e=r,s.d=a,s}function $p(e){return y(e=new this(e),e.e+1,this.rounding)}function Vp(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function qp(e){return new this(e).sin()}function Up(e){return new this(e).sinh()}function jp(e){return new this(e).sqrt()}function Bp(e,t){return new this(e).sub(t)}function Qp(){var e=0,t=arguments,r=new this(t[e]);for(x=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return x=!0,y(r,this.precision,this.rounding)}function Hp(e){return new this(e).tan()}function Gp(e){return new this(e).tanh()}function Wp(e){return y(e=new this(e),e.e+1,1)}f[Symbol.for("nodejs.util.inspect.custom")]=f.toString;f[Symbol.toStringTag]="Decimal";var Je=f.constructor=Ns(gi);Zr=new Je(Zr);Xr=new Je(Xr);var Z=Je;function Ke(e){return e===null?e:Array.isArray(e)?e.map(Ke):typeof e=="object"?Jp(e)?Kp(e):e.constructor!==null&&e.constructor.name!=="Object"?e:ut(e,Ke):e}function Jp(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Kp({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new Z(t);case"Json":return JSON.parse(t);default:me(t,"Unknown tagged value")}}var xe=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};function $e(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function Ms(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}function Yt(e){let t;return{get(){return t||(t={value:e()}),t.value}}}function zp(e){return{models:Ei(e.models),enums:Ei(e.enums),types:Ei(e.types)}}function Ei(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function dt(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function on(e){return e.toString()!=="Invalid Date"}function mt(e){return Je.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var sn={};qt(sn,{ModelAction:()=>ft,datamodelEnumToSchemaEnum:()=>Yp});function Yp(e){return{name:e.name,values:e.values.map(t=>t.name)}}var ft=(w=>(w.findUnique="findUnique",w.findUniqueOrThrow="findUniqueOrThrow",w.findFirst="findFirst",w.findFirstOrThrow="findFirstOrThrow",w.findMany="findMany",w.create="create",w.createMany="createMany",w.createManyAndReturn="createManyAndReturn",w.update="update",w.updateMany="updateMany",w.updateManyAndReturn="updateManyAndReturn",w.upsert="upsert",w.delete="delete",w.deleteMany="deleteMany",w.groupBy="groupBy",w.count="count",w.aggregate="aggregate",w.findRaw="findRaw",w.aggregateRaw="aggregateRaw",w))(ft||{});var Vs=ye(is());import rd from"node:fs";var Ls={keyword:Se,entity:Se,value:e=>X(We(e)),punctuation:We,directive:Se,function:Se,variable:e=>X(We(e)),string:e=>X(Ut(e)),boolean:Ge,number:Se,comment:jt};var Zp=e=>e,an={},Xp=0,v={manual:an.Prism&&an.Prism.manual,disableWorkerMessageHandler:an.Prism&&an.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof ge){let t=e;return new ge(t.type,v.util.encode(t.content),t.alias)}else return Array.isArray(e)?e.map(v.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++Xp}),e.__id},clone:function e(t,r){let n,i,o=v.util.type(t);switch(r=r||{},o){case"Object":if(i=v.util.objId(t),r[i])return r[i];n={},r[i]=n;for(let s in t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return i=v.util.objId(t),r[i]?r[i]:(n=[],r[i]=n,t.forEach(function(s,a){n[a]=e(s,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=v.util.clone(v.languages[e]);for(let n in t)r[n]=t[n];return r},insertBefore:function(e,t,r,n){n=n||v.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==t)for(let l in r)r.hasOwnProperty(l)&&(o[l]=r[l]);r.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,v.languages.DFS(v.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(t,r,n,i){i=i||{};let o=v.util.objId;for(let s in t)if(t.hasOwnProperty(s)){r.call(t,s,t[s],n||s);let a=t[s],l=v.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,r,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,r,s,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return v.hooks.run("before-tokenize",n),n.tokens=v.tokenize(n.code,n.grammar),v.hooks.run("after-tokenize",n),ge.stringify(v.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,o,s){for(let h in r){if(!r.hasOwnProperty(h)||!r[h])continue;if(h==s)return;let I=r[h];I=v.util.type(I)==="Array"?I:[I];for(let T=0;T<I.length;++T){let C=I[T],w=C.inside,k=!!C.lookbehind,de=!!C.greedy,le=0,$t=C.alias;if(de&&!C.pattern.global){let U=C.pattern.toString().match(/[imuy]*$/)[0];C.pattern=RegExp(C.pattern.source,U+"g")}C=C.pattern||C;for(let U=n,ne=i;U<t.length;ne+=t[U].length,++U){let Ae=t[U];if(t.length>e.length)return;if(Ae instanceof ge)continue;if(de&&U!=t.length-1){C.lastIndex=ne;var p=C.exec(e);if(!p)break;var c=p.index+(k?p[1].length:0),d=p.index+p[0].length,a=U,l=ne;for(let _=t.length;a<_&&(l<d||!t[a].type&&!t[a-1].greedy);++a)l+=t[a].length,c>=l&&(++U,ne=l);if(t[U]instanceof ge)continue;u=a-U,Ae=e.slice(ne,l),p.index-=ne}else{C.lastIndex=0;var p=C.exec(Ae),u=1}if(!p){if(o)break;continue}k&&(le=p[1]?p[1].length:0);var c=p.index+le,p=p[0].slice(le),d=c+p.length,m=Ae.slice(0,c),g=Ae.slice(d);let K=[U,u];m&&(++U,ne+=m.length,K.push(m));let at=new ge(h,w?v.tokenize(p,w):p,$t,p,de);if(K.push(at),g&&K.push(g),Array.prototype.splice.apply(t,K),u!=1&&v.matchGrammar(e,t,r,U,ne,!0,h),o)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let i in n)t[i]=n[i];delete t.rest}return v.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=v.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=v.hooks.all[e];if(!(!r||!r.length))for(var n=0,i;i=r[n++];)i(t)}},Token:ge};v.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};v.languages.javascript=v.languages.extend("clike",{"class-name":[v.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});v.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;v.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:v.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:v.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:v.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:v.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});v.languages.markup&&v.languages.markup.tag.addInlined("script","javascript");v.languages.js=v.languages.javascript;v.languages.typescript=v.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});v.languages.ts=v.languages.typescript;function ge(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=(n||"").length|0,this.greedy=!!i}ge.stringify=function(e,t){return typeof e=="string"?e:Array.isArray(e)?e.map(function(r){return ge.stringify(r,t)}).join(""):ed(e.type)(e.content)};function ed(e){return Ls[e]||Zp}function Fs(e){return td(e,v.languages.javascript)}function td(e,t){return v.tokenize(e,t).map(n=>ge.stringify(n)).join("")}function $s(e){return ri(e)}var ln=class e{firstLineNumber;lines;static read(t){let r;try{r=rd.readFileSync(t,"utf-8")}catch{return null}return e.fromContent(r)}static fromContent(t){let r=t.split(/\r?\n/);return new e(1,r)}constructor(t,r){this.firstLineNumber=t,this.lines=r}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(t,r){if(t<this.firstLineNumber||t>this.lines.length+this.firstLineNumber)return this;let n=t-this.firstLineNumber,i=[...this.lines];return i[n]=r(i[n]),new e(this.firstLineNumber,i)}mapLines(t){return new e(this.firstLineNumber,this.lines.map((r,n)=>t(r,this.firstLineNumber+n)))}lineAt(t){return this.lines[t-this.firstLineNumber]}prependSymbolAt(t,r){return this.mapLines((n,i)=>i===t?`${r} ${n}`:`  ${n}`)}slice(t,r){let n=this.lines.slice(t-1,r).join(`
`);return new e(t,$s(n).split(`
`))}highlight(){let t=Fs(this.toString());return new e(this.firstLineNumber,t.split(`
`))}toString(){return this.lines.join(`
`)}};var nd={red:Ce,gray:jt,dim:He,bold:X,underline:ie,highlightSource:e=>e.highlight()},id={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function od({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function sd({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},o){let s=od({message:t,originalMethod:r,isPanic:n,callArguments:i});if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),u=ln.read(a.fileName)?.slice(l,a.lineNumber),c=u?.lineAt(a.lineNumber);if(u&&c){let p=ld(c),d=ad(c);if(!d)return s;s.functionName=`${d.code})`,s.location=a,n||(u=u.mapLineAt(a.lineNumber,g=>g.slice(0,d.openingBraceIndex))),u=o.highlightSource(u);let m=String(u.lastLineNumber).length;if(s.contextLines=u.mapLines((g,h)=>o.gray(String(h).padStart(m))+" "+g).mapLines(g=>o.dim(g)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let g=p+m+1;g+=2,s.callArguments=(0,Vs.default)(i,g).slice(g)}}return s}function ad(e){let t=Object.keys(ft).join("|"),n=new RegExp(String.raw`\.(${t})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function ld(e){let t=0;for(let r=0;r<e.length;r++){if(e.charAt(r)!==" ")return t;t++}return t}function ud({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),t&&a.push(s.underline(cd(t))),i){a.push("");let u=[i.toString()];o&&(u.push(o),u.push(s.dim(")"))),a.push(u.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function cd(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function un(e){let t=e.showColors?nd:id,r;return r=sd(e,t),ud(r,t)}var Js=ye(xi());function Bs(e,t,r){let n=Qs(e),i=pd(n),o=md(i);o?cn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Qs(e){return e.errors.flatMap(t=>t.kind==="Union"?Qs(t):[t])}function pd(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:dd(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function dd(e,t){return[...new Set(e.concat(t))]}function md(e){return mi(e,(t,r)=>{let n=Us(t),i=Us(r);return n!==i?n-i:js(t)-js(r)})}function Us(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function js(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}var ue=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};Gs();var gt=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};Hs();var pn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};var dn=e=>e,mn={bold:dn,red:dn,green:dn,dim:dn,enabled:!1},Ws={bold:X,red:Ce,green:Ut,dim:He,enabled:!0},ht={write(e){e.writeLine(",")}};var be=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};var Ve=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var yt=class extends Ve{items=[];addItem(t){return this.items.push(new pn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new be("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(ht,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var wt=class e extends Ve{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof yt&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new be("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(ht,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};var H=class extends Ve{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new be(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};var Zt=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(ht,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function cn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":fd(e,t);break;case"IncludeOnScalar":gd(e,t);break;case"EmptySelection":hd(e,t,r);break;case"UnknownSelectionField":xd(e,t);break;case"InvalidSelectionValue":bd(e,t);break;case"UnknownArgument":Pd(e,t);break;case"UnknownInputField":vd(e,t);break;case"RequiredArgumentMissing":Td(e,t);break;case"InvalidArgumentType":Ad(e,t);break;case"InvalidArgumentValue":Cd(e,t);break;case"ValueTooLarge":Sd(e,t);break;case"SomeFieldsMissing":Rd(e,t);break;case"TooManyFieldsGiven":Id(e,t);break;case"Union":Bs(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function fd(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function gd(e,t){let[r,n]=Xt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new ue(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${er(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function hd(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){yd(e,t,i);return}if(n.hasField("select")){wd(e,t);return}}if(r?.[$e(e.outputType.name)]){Ed(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function yd(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new ue(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function wd(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Ys(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${er(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function Ed(e,t){let r=new Zt;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new ue("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=Xt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let l=a?.value.asObject()??new wt;l.addSuggestion(n),a.value=l}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function xd(e,t){let r=Zs(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Ys(n,e.outputType);break;case"include":kd(n,e.outputType);break;case"omit":Od(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(er(n)),i.join(" ")})}function bd(e,t){let r=Zs(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Pd(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),Dd(n,e.arguments)),t.addErrorMessage(i=>Ks(i,r,e.arguments.map(o=>o.name)))}function vd(e,t){let[r,n]=Xt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&Xs(o,e.inputType)}t.addErrorMessage(o=>Ks(o,n,e.inputType.fields.map(s=>s.name)))}function Ks(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=Nd(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(er(e)),n.join(" ")}function Td(e,t){let r;t.addErrorMessage(l=>r?.value instanceof H&&r.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=Xt(e.argumentPath),s=new Zt,a=n.getDeepFieldValue(i)?.asObject();if(a)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new ue(o,s).makeRequired())}else{let l=e.inputTypes.map(zs).join(" | ");a.addSuggestion(new ue(o,l).makeRequired())}}function zs(e){return e.kind==="list"?`${zs(e.elementType)}[]`:e.name}function Ad(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=fn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Cd(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=fn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Sd(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof H&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function Rd(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&Xs(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${fn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(er(i)),o.join(" ")})}function Id(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${fn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Ys(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ue(r.name,"true"))}function kd(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new ue(r.name,"true"))}function Od(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new ue(r.name,"true"))}function Dd(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ue(r.name,r.typeNames.join(" | ")))}function Zs(e,t){let[r,n]=Xt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),l=o?.getField(n);return o&&l?{parentKind:"select",parent:o,field:l,fieldName:n}:(l=s?.getField(n),s&&l?{parentKind:"include",field:l,parent:s,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"omit",field:l,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function Xs(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ue(r.name,r.typeNames.join(" | ")))}function Xt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function er({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function fn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var _d=3;function Nd(e,t){let r=1/0,n;for(let i of t){let o=(0,Js.default)(e,i);o>_d||o<r&&(r=o,n=i)}return n}var tr=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function Et(e){return e instanceof tr}var gn=Symbol(),Pi=new WeakMap,ke=class{constructor(t){t===gn?Pi.set(this,`Prisma.${this._getName()}`):Pi.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Pi.get(this)}},rr=class extends ke{_getNamespace(){return"NullTypes"}},nr=class extends rr{#e};Ti(nr,"DbNull");var ir=class extends rr{#e};Ti(ir,"JsonNull");var or=class extends rr{#e};Ti(or,"AnyNull");var vi={classes:{DbNull:nr,JsonNull:ir,AnyNull:or},instances:{DbNull:new nr(gn),JsonNull:new ir(gn),AnyNull:new or(gn)}};function Ti(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var ea=": ",hn=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+ea.length}write(t){let r=new be(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(ea).write(this.value)}};var Ai=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function xt(e){return new Ai(ta(e))}function ta(e){let t=new wt;for(let[r,n]of Object.entries(e)){let i=new hn(r,ra(n));t.addField(i)}return t}function ra(e){if(typeof e=="string")return new H(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new H(String(e));if(typeof e=="bigint")return new H(`${e}n`);if(e===null)return new H("null");if(e===void 0)return new H("undefined");if(mt(e))return new H(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new H(`Buffer.alloc(${e.byteLength})`):new H(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=on(e)?e.toISOString():"Invalid Date";return new H(`new Date("${t}")`)}return e instanceof ke?new H(`Prisma.${e._getName()}`):Et(e)?new H(`prisma.${$e(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Md(e):typeof e=="object"?ta(e):new H(Object.prototype.toString.call(e))}function Md(e){let t=new yt;for(let r of e)t.addItem(ra(r));return t}function yn(e,t){let r=t==="pretty"?Ws:mn,n=e.renderAllMessages(r),i=new gt(0,{colors:r}).write(e).toString();return{message:n,args:i}}function wn({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=xt(e);for(let p of t)cn(p,a,s);let{message:l,args:u}=yn(a,r),c=un({message:l,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:u});throw new te(c,{clientVersion:o})}function Pe(e){return e.replace(/^./,t=>t.toLowerCase())}function ia(e,t,r){let n=Pe(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Ld({...e,...na(t.name,e,t.result.$allModels),...na(t.name,e,t.result[n])})}function Ld(e){let t=new xe,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return ut(e,n=>({...n,needs:r(n.name,new Set)}))}function na(e,t,r){return r?ut(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Fd(t,o,i)})):{}}function Fd(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function oa(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function sa(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var En=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new xe;modelExtensionsCache=new xe;queryCallbacksCache=new xe;clientExtensions=Yt(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=Yt(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>ia(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=Pe(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},bt=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new En(t))}isEmpty(){return this.head===void 0}append(t){return new e(new En(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var xn=class{constructor(t){this.name=t}};function aa(e){return e instanceof xn}function $d(e){return new xn(e)}var la=Symbol(),sr=class{constructor(t){if(t!==la)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?Ci:t}},Ci=new sr(la);function ve(e){return e instanceof sr}var Vd={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},ua="explicitly `undefined` values are not allowed";function Ri({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=bt.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c}){let p=new Si({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c});return{modelName:e,action:Vd[t],query:ar(r,p)}}function ar({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:pa(r,n),selection:qd(e,t,i,n)}}function qd(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Qd(e,n)):Ud(n,t,r)}function Ud(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&jd(n,t,e),Bd(n,r,e),n}function jd(e,t,r){for(let[n,i]of Object.entries(t)){if(ve(i))continue;let o=r.nestSelection(n);if(Ii(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=ar(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=ar(i,o)}}function Bd(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=sa(i,n);for(let[s,a]of Object.entries(o)){if(ve(a))continue;Ii(a,r.nestSelection(s));let l=r.findField(s);n?.[s]&&!l||(e[s]=!a)}}function Qd(e,t){let r={},n=t.getComputedFields(),i=oa(e,n);for(let[o,s]of Object.entries(i)){if(ve(s))continue;let a=t.nestSelection(o);Ii(s,a);let l=t.findField(o);if(!(n?.[o]&&!l)){if(s===!1||s===void 0||ve(s)){r[o]=!1;continue}if(s===!0){l?.kind==="object"?r[o]=ar({},a):r[o]=!0;continue}r[o]=ar(s,a)}}return r}function ca(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(dt(e)){if(on(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(aa(e))return{$type:"Param",value:e.name};if(Et(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Hd(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:Buffer.from(r,n,i).toString("base64")}}if(Gd(e))return e.values;if(mt(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof ke){if(e!==vi.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Wd(e))return e.toJSON();if(typeof e=="object")return pa(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function pa(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);ve(i)||(i!==void 0?r[n]=ca(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:ua}))}return r}function Hd(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||ve(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(ca(o,i))}return r}function Gd(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Wd(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function Ii(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:ua})}var Si=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){wn({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[$e(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:me(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function da(e){if(!e._hasPreviewFlag("metrics"))throw new te("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var lr=class{_client;constructor(t){this._client=t}prometheus(t){return da(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return da(this._client),this._client._engine.metrics({format:"json",...t})}};function Jd(e,t){let r=Yt(()=>Kd(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function Kd(e){return{datamodel:{models:ki(e.models),enums:ki(e.enums),types:ki(e.types)}}}function ki(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}var Oi=new WeakMap,bn="$$PrismaTypedSql",ur=class{constructor(t,r){Oi.set(this,{sql:t,values:r}),Object.defineProperty(this,bn,{value:bn})}get sql(){return Oi.get(this).sql}get values(){return Oi.get(this).values}};function zd(e){return(...t)=>new ur(e,t)}function Pn(e){return e!=null&&e[bn]===bn}var rc=ye(ma());import{AsyncResource as Mg}from"node:async_hooks";import{EventEmitter as Lg}from"node:events";import Fg from"node:fs";import Mo from"node:path";var ce=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function Zd(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new ce([r,...Array(e.length-1).fill(t),n],e)}function fa(e){return new ce([e],[])}var Xd=fa("");function ga(e,...t){return new ce(e,t)}function cr(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}function re(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}function ze(e){let t=new xe;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}var vn={enumerable:!0,configurable:!0,writable:!0};function Tn(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>vn,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var ha=Symbol.for("nodejs.util.inspect.custom");function he(e,t){let r=em(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=ya(Reflect.ownKeys(o),r),a=ya(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=r.get(s);return l?l.getPropertyDescriptor?{...vn,...l?.getPropertyDescriptor(s)}:vn:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[ha]=function(){let o={...this};return delete o[ha],o},i}function em(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function ya(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}function Pt(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function vt(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function wa(e){if(e===void 0)return"";let t=xt(e);return new gt(0,{colors:mn}).write(t).toString()}var tm="P2037";function An({error:e,user_facing_error:t},r,n){return t.error_code?new Q(rm(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new ee(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function rm(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===tm&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}var pr="<unknown>";function Ea(e){var t=e.split(`
`);return t.reduce(function(r,n){var i=om(n)||am(n)||cm(n)||fm(n)||dm(n);return i&&r.push(i),r},[])}var nm=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,im=/\((\S*)(?::(\d+))(?::(\d+))\)/;function om(e){var t=nm.exec(e);if(!t)return null;var r=t[2]&&t[2].indexOf("native")===0,n=t[2]&&t[2].indexOf("eval")===0,i=im.exec(t[2]);return n&&i!=null&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||pr,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}var sm=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function am(e){var t=sm.exec(e);return t?{file:t[2],methodName:t[1]||pr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var lm=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,um=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function cm(e){var t=lm.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=um.exec(t[3]);return r&&n!=null&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||pr,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}var pm=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function dm(e){var t=pm.exec(e);return t?{file:t[3],methodName:t[1]||pr,arguments:[],lineNumber:+t[4],column:t[5]?+t[5]:null}:null}var mm=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function fm(e){var t=mm.exec(e);return t?{file:t[2],methodName:t[1]||pr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Di=class{getLocation(){return null}},_i=class{_error;constructor(){this._error=new Error}getLocation(){let t=this._error.stack;if(!t)return null;let n=Ea(t).find(i=>{if(!i.file)return!1;let o=si(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function qe(e){return e==="minimal"?typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Di:new _i}var xa={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Tt(e={}){let t=hm(e);return Object.entries(t).reduce((n,[i,o])=>(xa[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function hm(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Cn(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function ba(e,t){let r=Cn(e);return t({action:"aggregate",unpacker:r,argsMapper:Tt})(e)}function ym(e={}){let{select:t,...r}=e;return typeof t=="object"?Tt({...r,_count:t}):Tt({...r,_count:{_all:!0}})}function wm(e={}){return typeof e.select=="object"?t=>Cn(e)(t)._count:t=>Cn(e)(t)._count._all}function Pa(e,t){return t({action:"count",unpacker:wm(e),argsMapper:ym})(e)}function Em(e={}){let t=Tt(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function xm(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function va(e,t){return t({action:"groupBy",unpacker:xm(e),argsMapper:Em})(e)}function Ta(e,t,r){if(t==="aggregate")return n=>ba(n,r);if(t==="count")return n=>Pa(n,r);if(t==="groupBy")return n=>va(n,r)}function Aa(e,t){let r=t.fields.filter(i=>!i.relationName),n=Ms(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new tr(e,o,s.type,s.isList,s.kind==="enum")},...Tn(Object.keys(n))})}var Ca=e=>Array.isArray(e)?e:e.split("."),Ni=(e,t)=>Ca(t).reduce((r,n)=>r&&r[n],e),Sa=(e,t,r)=>Ca(t).reduceRight((n,i,o,s)=>Object.assign({},Ni(e,s.slice(0,o)),{[i]:n}),r);function bm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function Pm(e,t,r){return t===void 0?e??{}:Sa(t,r,e||!0)}function Mi(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((l,u)=>({...l,[u.name]:u}),{});return l=>{let u=qe(e._errorFormat),c=bm(n,i),p=Pm(l,o,c),d=r({dataPath:c,callsite:u})(p),m=vm(e,t);return new Proxy(d,{get(g,h){if(!m.includes(h))return g[h];let T=[a[h].type,r,h],C=[c,p];return Mi(e,...T,...C)},...Tn([...m,...Object.getOwnPropertyNames(d)])})}}function vm(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var Tm=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],Am=["aggregate","count","groupBy"];function Li(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[Cm(e,t),Rm(e,t),cr(r),re("name",()=>t),re("$name",()=>t),re("$parent",()=>e._appliedParent)];return he({},n)}function Cm(e,t){let r=Pe(t),n=Object.keys(ft).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>l=>{let u=qe(e._errorFormat);return e._createPrismaPromise(c=>{let p={args:l,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:c,callsite:u};return e._request({...p,...a})},{action:o,args:l,model:t})};return Tm.includes(o)?Mi(e,t,s):Sm(i)?Ta(e,i,s):s({})}}}function Sm(e){return Am.includes(e)}function Rm(e,t){return ze(re("fields",()=>{let r=e._runtimeDataModel.models[t];return Aa(t,r)}))}function Ra(e){return e.replace(/^./,t=>t.toUpperCase())}var Fi=Symbol();function dr(e){let t=[Im(e),km(e),re(Fi,()=>e),re("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(cr(r)),he(e,t)}function Im(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function km(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(Pe),n=[...new Set(t.concat(r))];return ze({getKeys(){return n},getPropertyValue(i){let o=Ra(i);if(e._runtimeDataModel.models[o]!==void 0)return Li(e,o);if(e._runtimeDataModel.models[i]!==void 0)return Li(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function Ia(e){return e[Fi]?e[Fi]:e}function ka(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return dr(t)}function Oa({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let l of Object.values(o)){if(n){if(n[l.name])continue;let u=l.needs.filter(c=>n[c]);u.length>0&&a.push(Pt(u))}else if(r){if(!r[l.name])continue;let u=l.needs.filter(c=>!r[c]);u.length>0&&a.push(Pt(u))}Om(e,l.needs)&&s.push(Dm(l,he(e,s)))}return s.length>0||a.length>0?he(e,[...s,...a]):e}function Om(e,t){return t.every(r=>di(e,r))}function Dm(e,t){return ze(re(e.name,()=>e.compute(t)))}function Sn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Sn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Da({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Da({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Da({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||ve(s))continue;let l=n.models[r].fields.find(c=>c.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let u=typeof s=="object"?s:{};t[o]=Sn({visitor:i,result:t[o],args:u,modelName:l.type,runtimeDataModel:n})}}function _a({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:Sn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,l,u)=>{let c=Pe(l);return Oa({result:a,modelName:c,select:u.select,omit:u.select?void 0:{...o?.[c],...u.omit},extensions:n})}})}var _m=["$connect","$disconnect","$on","$transaction","$use","$extends"],Na=_m;function Ma(e){if(e instanceof ce)return Nm(e);if(Pn(e))return Mm(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=mr(e[n]);return r}let t={};for(let r in e)t[r]=mr(e[r]);return t}function Nm(e){return new ce(e.strings,e.values)}function Mm(e){return new ur(e.sql,e.values)}function mr(e){if(typeof e!="object"||e==null||e instanceof ke||Et(e))return e;if(mt(e))return new Z(e.toFixed());if(dt(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=mr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:mr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=mr(e[r]);return t}me(e,"Unknown value")}function Fa(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Ma(t.args??{}),__internalParams:t,query:(s,a=t)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=Ua(o,l),a.args=s,Fa(e,a,r,n+1)}})})}function $a(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return Fa(e,t,s)}function Va(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?qa(r,n,0,e):e(r)}}function qa(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=Ua(i,l),qa(a,t,r+1,n)}})}var La=e=>e;function Ua(e=La,t=La){return r=>e(t(r))}var ja=q("prisma:client"),Ba={Vercel:"vercel","Netlify CI":"netlify"};function Qa({postinstall:e,ciName:t,clientVersion:r}){if(ja("checkPlatformCaching:postinstall",e),ja("checkPlatformCaching:ciName",t),e===!0&&t&&t in Ba){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${Ba[t]}-build`;throw console.error(n),new O(n,r)}}function Ha(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}var Lm=()=>globalThis.process?.release?.name==="node",Fm=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,$m=()=>!!globalThis.Deno,Vm=()=>typeof globalThis.Netlify=="object",qm=()=>typeof globalThis.EdgeRuntime=="object",Um=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function jm(){return[[Vm,"netlify"],[qm,"edge-light"],[Um,"workerd"],[$m,"deno"],[Fm,"bun"],[Lm,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var Bm={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function $i(){let e=jm();return{id:e,prettyName:Bm[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var Vi=ye(oi());function Ga(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}function Wa(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}var Ja=ye(ws());function Ka({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,Ja.default)({user:t,repo:r,template:n,title:e,body:i})}function za({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=Ko(6e3-(s?.length??0)),l=Wa((0,Vi.default)(a)),u=n?`# Description
\`\`\`
${n}
\`\`\``:"",c=(0,Vi.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${u}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?Ga(s):""}
\`\`\`
`),p=Ka({title:r,body:c});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${ie(p)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}function V(e,t){throw new Error(t)}function qi(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>qi(e[r],t[r]))}function fr(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]!=typeof t[o]){if(typeof e[o]=="number"||typeof t[o]=="number")return`${e[o]}`==`${t[o]}`;if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return BigInt(`${e[o]}`.replace(/n$/,""))===BigInt(`${t[o]}`.replace(/n$/,""));if(e[o]instanceof Date||t[o]instanceof Date)return new Date(`${e[o]}`).getTime()===new Date(`${t[o]}`).getTime();if(Z.isDecimal(e[o])||Z.isDecimal(t[o]))return new Z(`${e[o]}`).equals(new Z(`${t[o]}`))}return qi(e[o],t[o])})}function gr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?Buffer.from(r).toString("base64"):r)}var $=class extends Error{name="DataMapperError"};function Xa(e,t,r){switch(t.type){case"AffectedRows":if(typeof e!="number")throw new $(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"Object":return Ui(e,t.fields,r);case"Value":return ji(e,"<result>",t.resultType,r);default:V(t,`Invalid data mapping type: '${t.type}'`)}}function Ui(e,t,r){if(e===null)return null;if(Array.isArray(e))return e.map(i=>Ya(i,t,r));if(typeof e=="object")return Ya(e,t,r);if(typeof e=="string"){let n;try{n=JSON.parse(e)}catch(i){throw new $("Expected an array or object, got a string that is not valid JSON",{cause:i})}return Ui(n,t,r)}throw new $(`Expected an array or an object, got: ${typeof e}`)}function Ya(e,t,r){if(typeof e!="object")throw new $(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"AffectedRows":throw new $(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"Object":{if(!o.flattened&&!Object.hasOwn(e,i))throw new $(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.flattened?e:e[i];n[i]=Ui(s,o.fields,r);break}case"Value":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=ji(e[s],s,o.resultType,r);else throw new $(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:V(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function ji(e,t,r,n){if(e===null)return r.type==="Array"?[]:null;switch(r.type){case"Any":return e;case"String":{if(typeof e!="string")throw new $(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"Int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new $(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new $(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new $(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"BigInt":{if(typeof e!="number"&&typeof e!="string")throw new $(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"Float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new $(`Expected a float in column '${t}', got string: ${e}`);return i}throw new $(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"Boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new $(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}throw new $(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"Decimal":if(typeof e!="number"&&typeof e!="string"&&!Z.isDecimal(e))throw new $(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"Date":{if(typeof e=="string")return{$type:"DateTime",value:Za(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new $(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"Time":{if(typeof e=="string")return{$type:"DateTime",value:`1970-01-01T${Za(e)}`};throw new $(`Expected a time in column '${t}', got ${typeof e}: ${e}`)}case"Array":return e.map((o,s)=>ji(o,`${t}[${s}]`,r.inner,n));case"Object":return{$type:"Json",value:typeof e=="string"?e:gr(e)};case"Bytes":{if(typeof e=="string"&&e.startsWith("\\x"))return{$type:"Bytes",value:Buffer.from(e.slice(2),"hex").toString("base64")};if(Array.isArray(e))return{$type:"Bytes",value:Buffer.from(e).toString("base64")};throw new $(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`)}case"Enum":{let i=n[r.inner];if(i===void 0)throw new $(`Unknown enum '${r.inner}'`);let o=i[`${e}`];if(o===void 0)throw new $(`Unknown enum value '${e}' for enum '${r.inner}'`);return o}default:V(r,`DataMapper: Unknown result type: ${r.type}`)}}var Qm=/Z$|(?<!\d{4}-\d{2})[+-]\d{2}(:?\d{2})?$/;function Za(e){let t=Qm.exec(e);return t===null?`${e}Z`:t[0]!=="Z"&&t[1]===void 0?`${e}:00`:e}var hr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(hr||(hr={}));function Hm(e){switch(e){case"postgres":return"postgresql";case"mysql":return"mysql";case"sqlite":return"sqlite";default:V(e,`Unknown provider: ${e}`)}}async function Rn({query:e,queryable:t,tracingHelper:r,onQuery:n,execute:i}){return await r.runInChildSpan({name:"db_query",kind:hr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":Hm(t.provider)}},async()=>{let o=new Date,s=performance.now(),a=await i(),l=performance.now();return n?.({timestamp:o,duration:l-s,query:e.sql,params:e.args}),a})}function Bi(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}var A={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};var Te=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function el(e){if(!Bi(e))throw e;let t=Gm(e),r=Wm(e);throw!t||!r?e:new Te(r,t,{driverAdapterError:e})}function Gm(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":return"P2023";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":return;default:V(e.cause,`Unknown error: ${e.cause}`)}}function Wm(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${Qi({fields:e.cause.fields})}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${Qi(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${Qi({fields:e.cause.fields})}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Invalid isolation level \`${e.cause.level}\``;case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":return;default:V(e.cause,`Unknown error: ${e.cause}`)}}function Qi(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}function Ye(e,t){var r="000000000"+e;return r.substr(r.length-t)}import Jm from"node:os";function Km(){try{return Jm.hostname()}catch{return process.env._CLUSTER_NETWORK_NAME_||process.env.COMPUTERNAME||"hostname"}}var tl=2,zm=Ye(process.pid.toString(36),tl),rl=Km(),Ym=rl.length,Zm=Ye(rl.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+Ym+36).toString(36),tl);function Hi(){return zm+Zm}function In(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function Gi(e){let n=Math.pow(36,4),i=0;function o(){return Ye((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var l="c",u=new Date().getTime().toString(36),c=Ye(s().toString(36),4),p=e(),d=o()+o();return l+u+c+p+d}return a.fingerprint=e,a.isCuid=In,a}var Xm=Gi(Hi);var nl=Xm;var tu=ye(Wl());import{webcrypto as Kl}from"node:crypto";var Jl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var qf=128,Xe,St;function Uf(e){!Xe||Xe.length<e?(Xe=Buffer.allocUnsafe(e*qf),Kl.getRandomValues(Xe),St=0):St+e>Xe.length&&(Kl.getRandomValues(Xe),St=0),St+=e}function to(e=21){Uf(e|=0);let t="";for(let r=St-e;r<St;r++)t+=Jl[Xe[r]&63];return t}import _n from"node:crypto";var Yl="0123456789ABCDEFGHJKMNPQRSTVWXYZ",Pr=32;var jf=16,Zl=10,zl=0xffffffffffff;var et;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(et||(et={}));var tt=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function Bf(e){let t=Math.floor(e()*Pr);return t===Pr&&(t=Pr-1),Yl.charAt(t)}function Qf(e){let t=Hf(),r=t&&(t.crypto||t.msCrypto)||(typeof _n<"u"?_n:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(_n?.randomBytes)return()=>_n.randomBytes(1).readUInt8()/255;throw new tt(et.PRNGDetectFailure,"Failed to find a reliable PRNG")}function Hf(){return Jf()?self:typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:null}function Gf(e,t){let r="";for(;e>0;e--)r=Bf(t)+r;return r}function Wf(e,t=Zl){if(isNaN(e))throw new tt(et.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>zl)throw new tt(et.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${zl}: ${e}`);if(e<0)throw new tt(et.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new tt(et.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%Pr,n=Yl.charAt(r)+n,e=(e-r)/Pr;return n}function Jf(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function Xl(e,t){let r=t||Qf(),n=!e||isNaN(e)?Date.now():e;return Wf(n,Zl)+Gf(jf,r)}var J=[];for(let e=0;e<256;++e)J.push((e+256).toString(16).slice(1));function Nn(e,t=0){return(J[e[t+0]]+J[e[t+1]]+J[e[t+2]]+J[e[t+3]]+"-"+J[e[t+4]]+J[e[t+5]]+"-"+J[e[t+6]]+J[e[t+7]]+"-"+J[e[t+8]]+J[e[t+9]]+"-"+J[e[t+10]]+J[e[t+11]]+J[e[t+12]]+J[e[t+13]]+J[e[t+14]]+J[e[t+15]]).toLowerCase()}import{randomFillSync as Kf}from"node:crypto";var Ln=new Uint8Array(256),Mn=Ln.length;function Rt(){return Mn>Ln.length-16&&(Kf(Ln),Mn=0),Ln.slice(Mn,Mn+=16)}import{randomUUID as zf}from"node:crypto";var ro={randomUUID:zf};function Yf(e,t,r){if(ro.randomUUID&&!t&&!e)return ro.randomUUID();e=e||{};let n=e.random??e.rng?.()??Rt();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return Nn(n)}var no=Yf;var io={};function Zf(e,t,r){let n;if(e)n=eu(e.random??e.rng?.()??Rt(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Rt();Xf(io,i,o),n=eu(o,io.msecs,io.seq,t,r)}return t??Nn(n)}function Xf(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function eu(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var oo=Zf;var Fn=class{#e={};constructor(){this.register("uuid",new lo),this.register("cuid",new uo),this.register("ulid",new co),this.register("nanoid",new po),this.register("product",new mo)}snapshot(t){return Object.create(this.#e,{now:{value:t==="mysql"?new ao:new so}})}register(t,r){this.#e[t]=r}},so=class{#e=new Date;generate(){return this.#e.toISOString()}},ao=class{#e=new Date;generate(){return this.#e.toISOString().replace("T"," ").replace("Z","")}},lo=class{generate(t){if(t===4)return no();if(t===7)return oo();throw new Error("Invalid UUID generator arguments")}},uo=class{generate(t){if(t===1)return nl();if(t===2)return(0,tu.createId)();throw new Error("Invalid CUID generator arguments")}},co=class{generate(){return Xl()}},po=class{generate(t){if(typeof t=="number")return to(t);if(t===void 0)return to();throw new Error("Invalid Nanoid generator arguments")}},mo=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};function fo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function go(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function ru(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function nu(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bigint"}function yo(e,t,r){let n=e.type;switch(n){case"rawSql":return ou(e.sql,iu(e.params,t,r));case"templateSql":return eg(e.fragments,e.placeholderFormat,iu(e.params,t,r));default:V(n,"Invalid query type")}}function iu(e,t,r){return e.map(n=>vr(n,t,r))}function vr(e,t,r){let n=e;for(;rg(n);)if(fo(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(go(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(a=>vr(a,t,r)))}else V(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)?n=n.map(i=>vr(i,t,r)):ru(n)?n=Buffer.from(n.prisma__value,"base64"):nu(n)&&(n=BigInt(n.prisma__value)),n}function eg(e,t,r){let n=0,i=1,o=[],s=e.map(a=>{let l=a.type;switch(l){case"parameter":if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);return o.push(r[n++]),ho(t,i++);case"stringChunk":return a.value;case"parameterTuple":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let u=r[n++],c=Array.isArray(u)?u:[u];return`(${c.length==0?"NULL":c.map(d=>(o.push(d),ho(t,i++))).join(",")})`}case"parameterTupleList":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let u=r[n++];if(!Array.isArray(u))throw new Error("Malformed query template. Tuple list expected.");if(u.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");return u.map(p=>{if(!Array.isArray(p))throw new Error("Malformed query template. Tuple expected.");return`(${p.map(m=>(o.push(m),ho(t,i++))).join(",")})`}).join(",")}default:V(l,"Invalid fragment type")}}).join("");return ou(s,o)}function ho(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function ou(e,t){let r=t.map(n=>tg(n));return{sql:e,args:t,argTypes:r}}function tg(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":Buffer.isBuffer(e)?"Bytes":"Unknown"}function rg(e){return fo(e)||go(e)}function au(e){return e.rows.map(t=>t.reduce((r,n,i)=>{let o=e.columnNames[i].split("."),s=r;for(let a=0;a<o.length;a++){let l=o[a];a===o.length-1?s[l]=n:(s[l]===void 0&&(s[l]={}),s=s[l])}return r},{}))}function lu(e){let r=e.columnTypes.map(n=>su(n)).map(n=>{switch(n){case"int":return i=>i===null?null:typeof i=="number"?i:parseInt(`${i}`,10);case"bigint":return i=>i===null?null:typeof i=="bigint"?i:BigInt(`${i}`);case"json":return i=>typeof i=="string"?JSON.parse(i):i;case"bool":return i=>typeof i=="string"?i==="true"||i==="1":typeof i=="number"?i===1:i;default:return i=>i}});return{columns:e.columnNames,types:e.columnTypes.map(n=>su(n)),rows:e.rows.map(n=>n.map((i,o)=>r[o](i)))}}function su(e){switch(e){case A.Int32:return"int";case A.Int64:return"bigint";case A.Float:return"float";case A.Double:return"double";case A.Text:return"string";case A.Enum:return"enum";case A.Bytes:return"bytes";case A.Boolean:return"bool";case A.Character:return"char";case A.Numeric:return"decimal";case A.Json:return"json";case A.Uuid:return"uuid";case A.DateTime:return"datetime";case A.Date:return"date";case A.Time:return"time";case A.Int32Array:return"int-array";case A.Int64Array:return"bigint-array";case A.FloatArray:return"float-array";case A.DoubleArray:return"double-array";case A.TextArray:return"string-array";case A.EnumArray:return"string-array";case A.BytesArray:return"bytes-array";case A.BooleanArray:return"bool-array";case A.CharacterArray:return"char-array";case A.NumericArray:return"decimal-array";case A.JsonArray:return"json-array";case A.UuidArray:return"uuid-array";case A.DateTimeArray:return"datetime-array";case A.DateArray:return"date-array";case A.TimeArray:return"time-array";case A.UnknownNumber:return"unknown";case A.Set:return"string";default:V(e,`Unexpected column type: ${e}`)}}function uu(e,t,r){if(!t.every(n=>wo(e,n))){let n=ng(e,r),i=ig(r);throw new Te(n,i,r.context)}}function wo(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:V(t,`Unknown rule type: ${t.type}`)}}function ng(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:V(t,`Unknown error identifier: ${t}`)}}function ig(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:V(e,`Unknown error identifier: ${e}`)}}var It=class e{#e;#r;#t;#o=new Fn;#n;#s;#i;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s}){this.#e=t,this.#r=r,this.#t=n,this.#n=i,this.#s=o,this.#i=s??o}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:au,rawSerializer:lu})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#r,this.#o.snapshot(r.provider)).catch(i=>el(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!cu(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(Tr(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>pu(s)+pu(a)):0}}case"execute":{let o=yo(t.args,n,i);return this.#a(o,r,async()=>({value:await r.executeRaw(o)}))}case"query":{let o=yo(t.args,n,i);return this.#a(o,r,async()=>{let s=await r.queryRaw(o);return t.args.type==="rawSql"?{value:this.#i(s),lastInsertId:s.lastInsertId}:{value:this.#s(s),lastInsertId:s.lastInsertId}})}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(cu(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:hu(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async l=>({joinExpr:l,childRecords:(await this.interpretNode(l.child,r,n,i)).value})));if(Array.isArray(o)){for(let l of o)du(Ar(l),a);return{value:o,lastInsertId:s}}return{value:du(Ar(o),a),lastInsertId:s}}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),a=o.getTransaction(s,"query");try{let l=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),l}catch(l){throw await o.rollbackTransaction(s.id),l}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:Xa(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return uu(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return wo(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(Tr(s));return{value:Tr(o).filter(l=>!a.has(l))}}case"distinctBy":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=new Set,l=[];for(let u of Tr(o)){let c=gu(u,t.args.fields);a.has(c)||(a.add(c),l.push(u))}return{value:l,lastInsertId:s}}case"paginate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=Tr(o),l=t.args.pagination.linkingFields;if(l!==null){let u=new Map;for(let p of a){let d=gu(p,l);u.has(d)||u.set(d,[]),u.get(d).push(p)}let c=Array.from(u.entries());return c.sort(([p],[d])=>p<d?-1:p>d?1:0),{value:c.flatMap(([,p])=>fu(p,t.args.pagination)),lastInsertId:s}}return{value:fu(a,t.args.pagination),lastInsertId:s}}case"extendRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:Ar(o);for(let[l,u]of Object.entries(t.args.values))u.type==="lastInsertId"?a[l]=s:a[l]=vr(u.value,n,i);return{value:a,lastInsertId:s}}default:V(t,`Unexpected node type: ${t.type}`)}}#a(t,r,n){return Rn({query:t,queryable:r,execute:n,tracingHelper:this.#n,onQuery:this.#t})}};function cu(e){return Array.isArray(e)?e.length===0:e==null}function Tr(e){return Array.isArray(e)?e:[e]}function pu(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function Ar(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function hu(e,t){return Array.isArray(e)?e.map(r=>hu(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function du(e,t){for(let{joinExpr:r,childRecords:n}of t)e[r.parentField]=og(n,e,r);return e}function og(e,t,r){if(Array.isArray(e)){let n=e.filter(i=>mu(Ar(i),t,r));return r.isRelationUnique?n.length>0?n[0]:null:n}else{if(e===null)return null;{let n=Ar(e);return mu(n,t,r)?n:null}}}function mu(e,t,r){for(let[n,i]of r.on)if(t[n]!==e[i])return!1;return!0}function fu(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>fr(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function gu(e,t){return JSON.stringify(t.map(r=>e[r]))}async function sg(){return globalThis.crypto??await import("node:crypto")}async function yu(){return(await sg()).randomUUID()}var pe=class extends Te{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Cr=class extends pe{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},$n=class extends pe{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Vn=class extends pe{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},qn=class extends pe{constructor(){super("Unable to start a transaction in the given time.")}},Un=class extends pe{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:t,timeout:r,timeTaken:n})}},kt=class extends pe{constructor(t){super(`Internal Consistency Error: ${t}`)}},jn=class extends pe{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var ag=100,Sr=q("prisma:client:transactionManager"),lg=()=>({sql:"COMMIT",args:[],argTypes:[]}),ug=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),cg=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),pg=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),Rr=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#e;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#e=i}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await yu(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n),n.timer=this.startTransactionTimeout(n.id,r.maxWait);let i=await this.driverAdapter.startTransaction(r.isolationLevel);switch(n.status){case"waiting":return n.transaction=i,clearTimeout(n.timer),n.timer=void 0,n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"timed_out":throw new qn;case"running":case"committed":case"rolled_back":throw new kt(`Transaction in invalid state ${n.status} although it just finished startup.`);default:V(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new Cr;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Sr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"waiting":case"running":throw new kt("Active transaction found in closed transactions list.");case"committed":throw new $n(r);case"rolled_back":throw new Vn(r);case"timed_out":throw new Un(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Sr("Transaction not found.",t),new Cr}if(["committed","rolled_back","timed_out"].includes(n.status))throw new kt("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{Sr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):Sr("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){if(Sr("Closing transaction.",{transactionId:t.id,status:r}),t.status=r,t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#t(cg(),t.transaction,()=>t.transaction.commit());else{await t.transaction.commit();let n=lg();await this.#t(n,t.transaction,()=>t.transaction.executeRaw(n))}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#t(pg(),t.transaction,()=>t.transaction.rollback());else{await t.transaction.rollback();let n=ug();await this.#t(n,t.transaction,()=>t.transaction.executeRaw(n))}clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>ag&&this.closedTransactions.shift()}validateOptions(t){if(!t.timeout)throw new pe("timeout is required");if(!t.maxWait)throw new pe("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new jn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#t(t,r,n){return Rn({query:t,queryable:r,execute:n,tracingHelper:this.tracingHelper,onQuery:this.#e})}};var Bn="6.9.0";var Eo,wu={async loadQueryCompiler(e){let{clientVersion:t,adapter:r,compilerWasm:n}=e;if(r===void 0)throw new O(`The \`adapter\` option for \`PrismaClient\` is required in this context (${$i().prettyName})`,t);if(n===void 0)throw new O("WASM query compiler was unexpectedly `undefined`",t);return Eo===void 0&&(Eo=(async()=>{let i=await n.getRuntime(),o=await n.getQueryCompilerWasmModule();if(o==null)throw new O("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let s={"./query_compiler_bg.js":i},a=new WebAssembly.Instance(o,s),l=a.exports.__wbindgen_start;return i.__wbg_set_wasm(a.exports),l(),i.QueryCompiler})()),await Eo}};var Eu="P2038",Qn=q("prisma:client:clientEngine"),bu=globalThis;bu.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new oe(e,Bn)}};var Ot=class{name="ClientEngine";queryCompiler;instantiateQueryCompilerPromise;QueryCompilerConstructor;queryCompilerLoader;adapterPromise;transactionManagerPromise;config;provider;datamodel;logEmitter;logQueries;logLevel;lastStartedQuery;tracingHelper;#e;constructor(t,r){if(!t.previewFeatures?.includes("driverAdapters"))throw new O("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,Eu);if(t.adapter)this.adapterPromise=t.adapter.connect(),this.provider=t.adapter.provider,Qn("Using driver adapter: %O",t.adapter);else throw new O("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,Eu);this.queryCompilerLoader=r??wu,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#e=n=>{this.logEmitter.emit("query",{...n,params:gr(n.params),target:"ClientEngine"})}),this.transactionManagerPromise=this.adapterPromise.then(n=>new Rr({driverAdapter:n,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#i(this.config.transactionOptions.isolationLevel)},tracingHelper:this.tracingHelper,onQuery:this.#e})),this.instantiateQueryCompilerPromise=this.instantiateQueryCompiler()}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async instantiateQueryCompiler(){if(this.queryCompiler)return;this.QueryCompilerConstructor||(this.QueryCompilerConstructor=await this.queryCompilerLoader.loadQueryCompiler(this.config));let r=(await this.adapterPromise)?.getConnectionInfo?.()??{};try{this.#n(()=>{this.queryCompiler=new this.QueryCompilerConstructor({datamodel:this.datamodel,provider:this.provider,connectionInfo:r})})}catch(n){throw this.#r(n)}}#r(t){if(t instanceof oe)return t;try{let r=JSON.parse(t.message);return new O(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#t(t){if(t instanceof O)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new oe(xu(this,t.message),this.config.clientVersion);if(t instanceof Te)return new Q(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let r=JSON.parse(t);return new ee(`${r.message}
${r.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#o(t){return t instanceof oe?t:typeof t.message=="string"&&typeof t.code=="string"?new Q(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):t}#n(t){let r=bu.PRISMA_WASM_PANIC_REGISTRY.set_message,n;global.PRISMA_WASM_PANIC_REGISTRY.set_message=i=>{n=i};try{return t()}finally{if(global.PRISMA_WASM_PANIC_REGISTRY.set_message=r,n)throw new oe(xu(this,n),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.tracingHelper.runInChildSpan("connect",()=>this.ensureStarted())}async stop(){await this.tracingHelper.runInChildSpan("disconnect",async()=>{await this.instantiateQueryCompilerPromise,await(await this.transactionManagerPromise)?.cancelAllTransactions(),await(await this.adapterPromise).dispose()})}async ensureStarted(){let t=await this.adapterPromise,r=await this.transactionManagerPromise;return await this.instantiateQueryCompilerPromise,[t,r]}version(){return"unknown"}async transaction(t,r,n){let i,o=await this.transactionManagerPromise;try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#i(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s.id)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s.id)}else me(t,"Invalid transaction action.")}catch(s){throw this.#t(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{traceparent:r,interactiveTransaction:n}){Qn("sending request");let i=JSON.stringify(t);this.lastStartedQuery=i;let[o,s]=await this.ensureStarted().catch(l=>{throw this.#t(l)}),a;try{a=this.#n(()=>this.queryCompiler.compile(i))}catch(l){throw this.#o(l)}try{let l=JSON.parse(a);Qn("query plan created",a);let u=n?s.getTransaction(n,"query"):o,c=n?{enabled:!1}:{enabled:!0,manager:s},p={},m=await It.forSql({transactionManager:c,placeholderValues:p,onQuery:this.#e,tracingHelper:this.tracingHelper}).run(l,u);return Qn("query plan executed"),{data:{[t.action]:m}}}catch(l){throw this.#t(l)}}async requestBatch(t,{transaction:r,traceparent:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(vt(t,r));this.lastStartedQuery=o;let[,s]=await this.ensureStarted().catch(l=>{throw this.#t(l)}),a;try{a=this.queryCompiler.compileBatch(o)}catch(l){throw this.#o(l)}try{let l;if(r?.kind==="itx")l=r.options;else{let m=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;l=await this.transaction("start",{},m)}let u={},c=It.forSql({transactionManager:{enabled:!1},placeholderValues:u,onQuery:this.#e,tracingHelper:this.tracingHelper}),p=s.getTransaction(l,"batch query"),d=[];switch(a.type){case"multi":{d=await Promise.all(a.plans.map((m,g)=>c.run(m,p).then(h=>({data:{[t[g].action]:h}}),h=>h)));break}case"compacted":{if(!t.every(g=>g.action===i))throw new Error("All queries in a batch must have the same action");let m=await c.run(a.plan,p);d=this.#s(m,a,i);break}}return r?.kind!=="itx"&&await this.transaction("commit",{},l),d}catch(l){throw this.#t(l)}}metrics(t){throw new Error("Method not implemented.")}#s(t,r,n){let i=t.map(s=>r.keys.reduce((a,l)=>(a[l]=Ke(s[l]),a),{})),o=new Set(r.nestedSelection);return r.arguments.map(s=>{let a=i.findIndex(l=>fr(l,s));if(a===-1)return r.expectNonEmpty?new Q("An operation failed because it depends on one or more records that were required but not found",{code:"P2025",clientVersion:this.config.clientVersion}):{data:{[n]:null}};{let l=Object.entries(t[a]).filter(([u])=>o.has(u));return{data:{[n]:Object.fromEntries(l)}}}})}#i(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new Q(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function xu(e,t){return za({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:e.lastStartedQuery})}function Dt({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw new O(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new O("error: Missing URL environment variable, value, or override.",n);return i}var Hn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var ae=class extends Hn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};function S(e,t){return{...e,isRetryable:t}}var _t=class extends ae{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",S(t,!0))}};b(_t,"ForcedRetryError");var rt=class extends ae{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,S(r,!1))}};b(rt,"InvalidDatasourceError");var nt=class extends ae{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,S(r,!1))}};b(nt,"NotImplementedYetError");var L=class extends ae{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var it=class extends L{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",S(t,!0))}};b(it,"SchemaMissingError");var xo="This request could not be understood by the server",Ir=class extends L{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||xo,S(t,!1)),n&&(this.code=n)}};b(Ir,"BadRequestError");var kr=class extends L{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",S(t,!0)),this.logs=r}};b(kr,"HealthcheckTimeoutError");var Or=class extends L{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,S(t,!0)),this.logs=n}};b(Or,"EngineStartupError");var Dr=class extends L{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",S(t,!1))}};b(Dr,"EngineVersionNotSupportedError");var bo="Request timed out",_r=class extends L{name="GatewayTimeoutError";code="P5009";constructor(t,r=bo){super(r,S(t,!1))}};b(_r,"GatewayTimeoutError");var mg="Interactive transaction error",Nr=class extends L{name="InteractiveTransactionError";code="P5015";constructor(t,r=mg){super(r,S(t,!1))}};b(Nr,"InteractiveTransactionError");var fg="Request parameters are invalid",Mr=class extends L{name="InvalidRequestError";code="P5011";constructor(t,r=fg){super(r,S(t,!1))}};b(Mr,"InvalidRequestError");var Po="Requested resource does not exist",Lr=class extends L{name="NotFoundError";code="P5003";constructor(t,r=Po){super(r,S(t,!1))}};b(Lr,"NotFoundError");var vo="Unknown server error",Nt=class extends L{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||vo,S(t,!0)),this.logs=n}};b(Nt,"ServerError");var To="Unauthorized, check your connection string",Fr=class extends L{name="UnauthorizedError";code="P5007";constructor(t,r=To){super(r,S(t,!1))}};b(Fr,"UnauthorizedError");var Ao="Usage exceeded, retry again later",$r=class extends L{name="UsageExceededError";code="P5008";constructor(t,r=Ao){super(r,S(t,!0))}};b($r,"UsageExceededError");async function gg(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Vr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await gg(e);if(n.type==="QueryEngineError")throw new Q(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new Nt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new it(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Dr(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Or(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new O(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new kr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Nr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Mr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Fr(r,Mt(To,n));if(e.status===404)return new Lr(r,Mt(Po,n));if(e.status===429)throw new $r(r,Mt(Ao,n));if(e.status===504)throw new _r(r,Mt(bo,n));if(e.status>=500)throw new Nt(r,Mt(vo,n));if(e.status>=400)throw new Ir(r,Mt(xo,n))}function Mt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}function Pu(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}var _e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function vu(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,l,u,c;for(let p=0;p<o;p=p+3)c=t[p]<<16|t[p+1]<<8|t[p+2],s=(c&16515072)>>18,a=(c&258048)>>12,l=(c&4032)>>6,u=c&63,r+=_e[s]+_e[a]+_e[l]+_e[u];return i==1?(c=t[o],s=(c&252)>>2,a=(c&3)<<4,r+=_e[s]+_e[a]+"=="):i==2&&(c=t[o]<<8|t[o+1],s=(c&64512)>>10,a=(c&1008)>>4,l=(c&15)<<2,r+=_e[s]+_e[a]+_e[l]+"="),r}function Tu(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new O("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}function hg(e){return e[0]*1e3+e[1]/1e6}function Co(e){return new Date(hg(e))}var Au={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};var qr=class extends ae{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,S(r,!0))}};b(qr,"RequestError");async function ot(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new qr(a,{clientVersion:n,cause:s})}}var wg=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,Cu=q("prisma:client:dataproxyEngine");async function Eg(e,t){let r=Au["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&wg.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,l,u]=s.split("."),c=xg(`<=${a}.${l}.${u}`),p=await ot(c,{clientVersion:n});if(!p.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text()||"<empty body>"}`);let d=await p.text();Cu("length of body fetched from unpkg.com",d.length);let m;try{m=JSON.parse(d)}catch(g){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),g}return m.version}throw new nt("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function Su(e,t){let r=await Eg(e,t);return Cu("version",r),r}function xg(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Ru=3,Ur=q("prisma:client:dataproxyEngine"),So=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,interactiveTransaction:r}={}){let n={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-transaction-id"]=r.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}},jr=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){Tu(t),this.config=t,this.env={...t.env,...typeof process<"u"?process.env:{}},this.inlineSchema=vu(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.headerBuilder=new So({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.protocol=ni(r)?"http":"https",this.remoteClientVersion=await Su(this.host,this.config),Ur("host",this.host),Ur("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":Ur(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:Co(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:Co(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await ot(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||Ur("schema response status",r.status);let n=await Vr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=vt(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(l=>(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l?this.convertProtocolErrorsToClientError(l.errors):l))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await ot(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,interactiveTransaction:i}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||Ur("graphql response status",a.status),await this.handleError(await Vr(a,this.clientVersion));let l=await a.json();if(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l)throw this.convertProtocolErrorsToClientError(l.errors);return"batchResult"in l?l.batchResult:l}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let l=await ot(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Vr(l,this.clientVersion));let u=await l.json(),{extensions:c}=u;c&&this.propagateResponseExtensions(c);let p=u.id,d=u["data-proxy"].endpoint;return{id:p,payload:{endpoint:d}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await ot(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Vr(a,this.clientVersion));let l=await a.json(),{extensions:u}=l;u&&this.propagateResponseExtensions(u);return}}})}getURLAndAPIKey(){let t={clientVersion:this.clientVersion},r=Object.keys(this.inlineDatasources)[0],n=Dt({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(n)}catch{throw new rt(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==Hr)throw new rt(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new rt(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);return{apiKey:a,url:i}}metrics(){throw new nt("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof ae)||!i.isRetryable)throw i;if(r>=Ru)throw i instanceof _t?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${Ru} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await Pu(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof it)throw await this.uploadSchema(),new _t({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?An(t[0],this.config.clientVersion,this.config.activeProvider):new ee(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function Iu({copyEngine:e=!0},t){let r;try{r=Dt({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let n=!!(r?.startsWith("prisma://")||Gr(r));e&&n&&Kr("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)");let i=lt(t.generator),o=n||!e,s=!!t.adapter,a=i==="library",l=i==="binary",u=i==="client";if(o&&s||s&&!1){let c;throw e?r?.startsWith("prisma://")?c=["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:c=["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:c=["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."],new te(c.join(`
`),{clientVersion:t.clientVersion})}return o?new jr(t):u?new Ot(t):new Ot(t)}function Gn({generator:e}){return e?.previewFeatures??[]}var ku=e=>({command:e});var Ou=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);function Lt(e){try{return Du(e,"fast")}catch{return Du(e,"slow")}}function Du(e,t){return JSON.stringify(e.map(r=>Nu(r,t)))}function Nu(e,t){if(Array.isArray(e))return e.map(r=>Nu(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(dt(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(Z.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(Buffer.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(bg(e))return{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:Buffer.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Mu(e):e}function bg(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Mu(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(_u);let t={};for(let r of Object.keys(e))t[r]=_u(e[r]);return t}function _u(e){return typeof e=="bigint"?e.toString():Mu(e)}var Pg=/^(\s*alter\s)/i,Lu=q("prisma:client");function Ro(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&Pg.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var Io=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(Pn(r))n=r.sql,i={values:Lt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:Lt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:Lt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:Lt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=Ou(r),i={values:Lt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Lu(`prisma.${e}(${n}, ${i.values})`):Lu(`prisma.${e}(${n})`),{query:n,parameters:i}},Fu={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new ce(t,r)}},$u={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function ko(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=Vu(r(s)):Vu(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function Vu(e){return typeof e.then=="function"?e:Promise.resolve(e)}var vg=ti.split(".")[0],Tg={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Oo=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${vg}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??Tg}};function qu(){return new Oo}function Uu(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}function ju(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}var Wn=class{_middlewares=[];use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};var Qu=ye(oi());function Jn(e){return typeof e.batchRequestIdx=="number"}function Bu(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(Do(e.query.arguments)),t.push(Do(e.query.selection)),t.join("")}function Do(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${Do(n)})`:r}).join(" ")})`}var Ag={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function _o(e){return Ag[e]}var Kn=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function st(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new Z(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>st("bigint",r));case"bytes-array":return t.map(r=>st("bytes",r));case"decimal-array":return t.map(r=>st("decimal",r));case"datetime-array":return t.map(r=>st("datetime",r));case"date-array":return t.map(r=>st("date",r));case"time-array":return t.map(r=>st("time",r));default:return t}}function No(e){let t=[],r=Cg(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=st(e.types[s],i[s]);t.push(o)}return t}function Cg(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var Sg=q("prisma:client:request_handler"),zn=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new Kn({batchLoader:Va(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(p=>p.protocolQuery),l=this.client._tracingHelper.getTraceParent(s),u=n.some(p=>_o(p.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:l,transaction:Rg(o),containsWrite:u,customDataProxyFetch:i})).map((p,d)=>{if(p instanceof Error)return p;try{return this.mapQueryEngineResult(n[d],p)}catch(m){return m}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Hu(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:_o(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Bu(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return process.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(Sg(t),Ig(t,i))throw t;if(t instanceof Q&&kg(t)){let u=Gu(t.meta);wn({args:o,errors:[u],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let l=t.message;if(n&&(l=un({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:l})),l=this.sanitizeMessage(l),t.code){let u=s?{modelName:s,...t.meta}:t.meta;throw new Q(l,{code:t.code,clientVersion:this.client._clientVersion,meta:u,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new oe(l,this.client._clientVersion);if(t instanceof ee)throw new ee(l,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof O)throw new O(l,this.client._clientVersion);if(t instanceof oe)throw new oe(l,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Qu.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(u=>u!=="select"&&u!=="include"),a=Ni(o,s),l=i==="queryRaw"?No(a):Ke(a);return n?n(l):l}get[Symbol.toStringTag](){return"RequestHandler"}};function Rg(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Hu(e)};me(e,"Unknown transaction kind")}}function Hu(e){return{id:e.id,payload:e.payload}}function Ig(e,t){return Jn(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function kg(e){return e.code==="P2009"||e.code==="P2012"}function Gu(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Gu)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}var Wu=Bn;var Zu=ye(xi());var D=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};b(D,"PrismaClientConstructorValidationError");var Ju=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],Ku=["pretty","colorless","minimal"],zu=["info","query","warn","error"],Og={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new D(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Ft(r,t)||` Available datasources: ${t.join(", ")}`;throw new D(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new D(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new D(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new D(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&lt(t.generator)==="client")throw new D('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new D('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!Gn(t).includes("driverAdapters"))throw new D('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(lt(t.generator)==="binary")throw new D('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new D(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new D(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!Ku.includes(e)){let t=Ft(e,Ku);throw new D(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new D(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!zu.includes(r)){let n=Ft(r,zu);throw new D(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Ft(i,o);throw new D(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new D(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new D(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new D(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new D('"omit" option is expected to be an object.');if(e===null)throw new D('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=_g(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let l=o.fields.find(u=>u.name===s);if(!l){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(l.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new D(Ng(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new D(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Ft(r,t);throw new D(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Xu(e,t){for(let[r,n]of Object.entries(e)){if(!Ju.includes(r)){let i=Ft(r,Ju);throw new D(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Og[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new D('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Ft(e,t){if(t.length===0||typeof e!="string")return"";let r=Dg(e,t);return r?` Did you mean "${r}"?`:""}function Dg(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Zu.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function _g(e,t){return Yu(t.models,e)??Yu(t.types,e)}function Yu(e,t){let r=Object.keys(e).find(n=>$e(n)===t);if(r)return e[r]}function Ng(e,t){let r=xt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=yn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}function ec(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},l=u=>{o||(o=!0,r(u))};for(let u=0;u<e.length;u++)e[u].then(c=>{n[u]=c,a()},c=>{if(!Jn(c)){l(c);return}c.batchRequestIdx===u?l(c):(i||(i=c),a())})})}var Qe=q("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var $g={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Vg=Symbol.for("prisma.client.transaction.id"),qg={id:0,nextId(){return++this.id}};function Ug(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new Wn;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=ko();constructor(n){e=n?.__internal?.configOverride?.(e)??e,Qa(e),n&&Xu(n,e);let i=new Lg().on("error",()=>{});this._extensions=bt.empty(),this._previewFeatures=Gn(e),this._clientVersion=e.clientVersion??Wu,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=qu();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&Mo.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&Mo.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let l=e.activeProvider==="postgresql"?"postgres":e.activeProvider;if(s.provider!==l)throw new O(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${l}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new O("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=!s&&o&&Kt(o,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{let l=n??{},u=l.__internal??{},c=u.debug===!0;c&&q.enable("prisma:client");let p=Mo.resolve(e.dirname,e.relativePath);Fg.existsSync(p)||(p=e.dirname),Qe("dirname",e.dirname),Qe("relativePath",e.relativePath),Qe("cwd",p);let d=u.engine||{};if(l.errorFormat?this._errorFormat=l.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:p,dirname:e.dirname,enableDebugLogs:c,allowTriggerPanic:d.allowTriggerPanic,prismaPath:d.binaryPath??void 0,engineEndpoint:d.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:l.log&&ju(l.log),logQueries:l.log&&!!(typeof l.log=="string"?l.log==="query":l.log.find(m=>typeof m=="string"?m==="query":m.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:Ha(l,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:l.transactionOptions?.maxWait??2e3,timeout:l.transactionOptions?.timeout??5e3,isolationLevel:l.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Dt,getBatchRequestPayload:vt,prismaGraphQLToJSError:An,PrismaClientUnknownRequestError:ee,PrismaClientInitializationError:O,PrismaClientKnownRequestError:Q,debug:q("prisma:client:accelerateEngine"),engineVersion:rc.version,clientVersion:e.clientVersion}},Qe("clientVersion",e.clientVersion),this._engine=Iu(e,this._engineConfig),this._requestHandler=new zn(this,i),l.log)for(let m of l.log){let g=typeof m=="string"?m:m.emit==="stdout"?m.level:null;g&&this.$on(g,h=>{Gt.log(`${Gt.tags[g]??""}`,h.message||h.query)})}}catch(l){throw l.clientVersion=this._clientVersion,l}return this._appliedParent=dr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{zo()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:Io({clientMethod:i,activeProvider:a}),callsite:qe(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=tc(n,i);return Ro(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new te("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(Ro(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new te(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:ku,callsite:qe(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:Io({clientMethod:i,activeProvider:a}),callsite:qe(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...tc(n,i));throw new te("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new te("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=qg.nextId(),s=Uu(n.length),a=n.map((l,u)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let c=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,p={kind:"batch",id:o,index:u,isolationLevel:c,lock:s};return l.requestTransaction?.(p)??l});return ec(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),l;try{let u={kind:"itx",...a};l=await n(this._createItxClient(u)),await this._engine.transaction("commit",o,a)}catch(u){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),u}return l}_createItxClient(n){return he(dr(he(Ia(this),[re("_appliedParent",()=>this._appliedParent._createItxClient(n)),re("_createPrismaPromise",()=>ko(n)),re(Vg,()=>n.id)])),[Pt(Na)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??$g,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,l=async u=>{let c=this._middlewares.get(++a);if(c)return this._tracingHelper.runInChildSpan(s.middleware,I=>c(u,T=>(I?.end(),l(T))));let{runInTransaction:p,args:d,...m}=u,g={...n,...m};d&&(g.args=i.middlewareArgsToRequestArgs(d)),n.transaction!==void 0&&p===!1&&delete g.transaction;let h=await $a(this,g);return g.model?_a({result:h,modelName:g.model,args:g.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):h};return this._tracingHelper.runInChildSpan(s.operation,()=>new Mg("prisma-client-request").runInAsyncScope(()=>l(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:u,transaction:c,unpacker:p,otelParentCtx:d,customDataProxyFetch:m}){try{n=u?u(n):n;let g={name:"serialize"},h=this._tracingHelper.runInChildSpan(g,()=>Ri({modelName:l,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return q.enabled("prisma:client")&&(Qe("Prisma Client call:"),Qe(`prisma.${i}(${wa(n)})`),Qe("Generated request:"),Qe(JSON.stringify(h,null,2)+`
`)),c?.kind==="batch"&&await c.lock,this._requestHandler.request({protocolQuery:h,modelName:l,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:c,unpacker:p,otelParentCtx:d,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:m})}catch(g){throw g.clientVersion=this._clientVersion,g}}$metrics=new lr(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=ka}return t}function tc(e,t){return jg(e)?[new ce(e,t),Fu]:[e,$u]}function jg(e){return Array.isArray(e)&&Array.isArray(e.raw)}var Bg=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Qg(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!Bg.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}function Hg(e){Kt(e,{conflictCheck:"warn"})}export{sn as DMMF,q as Debug,Z as Decimal,Vo as Extensions,lr as MetricsClient,O as PrismaClientInitializationError,Q as PrismaClientKnownRequestError,oe as PrismaClientRustPanicError,ee as PrismaClientUnknownRequestError,te as PrismaClientValidationError,Uo as Public,ce as Sql,$d as createParam,Jd as defineDmmfProperty,Ke as deserializeJsonResponse,No as deserializeRawResult,zp as dmmfToRuntimeDataModel,Xd as empty,Ug as getPrismaClient,$i as getRuntime,Zd as join,Qg as makeStrictEnum,zd as makeTypedQueryFactory,vi as objectEnumValues,fa as raw,Ri as serializeJsonQuery,Ci as skip,ga as sqltag,Hg as warnEnvConflicts,Kr as warnOnce};
/*! Bundled license information:

@noble/hashes/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.5.0
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=client.mjs.map
