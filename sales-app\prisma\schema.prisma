// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// جدول العملاء
model Customer {
  id        Int      @id @default(autoincrement())
  name      String
  email     String?  @unique
  phone     String?
  address   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات
  sales     Sale[]
}

// جدول المنتجات
model Product {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  price       Float
  stock       Int      @default(0)
  category    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  saleItems   SaleItem[]
}

// جدول المبيعات
model Sale {
  id         Int      @id @default(autoincrement())
  customerId Int
  total      Float
  discount   Float    @default(0)
  tax        Float    @default(0)
  finalTotal Float
  status     String   @default("completed") // completed, pending, cancelled
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // العلاقات
  customer   Customer   @relation(fields: [customerId], references: [id])
  saleItems  SaleItem[]
}

// جدول تفاصيل المبيعات
model SaleItem {
  id        Int   @id @default(autoincrement())
  saleId    Int
  productId Int
  quantity  Int
  unitPrice Float
  total     Float

  // العلاقات
  sale      Sale    @relation(fields: [saleId], references: [id])
  product   Product @relation(fields: [productId], references: [id])
}
