import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // إضافة عملاء تجريبيين
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '0501234567',
        address: 'الرياض، المملكة العربية السعودية'
      }
    }),
    prisma.customer.create({
      data: {
        name: 'فاطمة علي',
        email: '<EMAIL>',
        phone: '0507654321',
        address: 'جدة، المملكة العربية السعودية'
      }
    }),
    prisma.customer.create({
      data: {
        name: 'محمد السعيد',
        phone: '0509876543',
        address: 'الدمام، المملكة العربية السعودية'
      }
    })
  ])

  // إضافة منتجات تجريبية
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'لا<PERSON><PERSON><PERSON>ب Dell',
        description: 'لابتوب Dell Inspiron 15 3000',
        price: 2500.00,
        stock: 10,
        category: 'إلكترونيات'
      }
    }),
    prisma.product.create({
      data: {
        name: 'ماوس لاسلكي',
        description: 'ماوس لاسلكي Logitech',
        price: 150.00,
        stock: 25,
        category: 'إلكترونيات'
      }
    }),
    prisma.product.create({
      data: {
        name: 'كيبورد ميكانيكي',
        description: 'كيبورد ميكانيكي للألعاب',
        price: 300.00,
        stock: 15,
        category: 'إلكترونيات'
      }
    }),
    prisma.product.create({
      data: {
        name: 'شاشة 24 بوصة',
        description: 'شاشة LED 24 بوصة Full HD',
        price: 800.00,
        stock: 8,
        category: 'إلكترونيات'
      }
    }),
    prisma.product.create({
      data: {
        name: 'كتاب البرمجة',
        description: 'كتاب تعلم البرمجة للمبتدئين',
        price: 75.00,
        stock: 20,
        category: 'كتب'
      }
    })
  ])

  // إضافة مبيعة تجريبية
  const sale = await prisma.sale.create({
    data: {
      customerId: customers[0].id,
      total: 2650.00,
      discount: 50.00,
      tax: 132.50,
      finalTotal: 2732.50,
      saleItems: {
        create: [
          {
            productId: products[0].id,
            quantity: 1,
            unitPrice: 2500.00,
            total: 2500.00
          },
          {
            productId: products[1].id,
            quantity: 1,
            unitPrice: 150.00,
            total: 150.00
          }
        ]
      }
    }
  })

  console.log('تم إنشاء البيانات التجريبية بنجاح!')
  console.log(`تم إنشاء ${customers.length} عملاء`)
  console.log(`تم إنشاء ${products.length} منتجات`)
  console.log(`تم إنشاء مبيعة واحدة`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
